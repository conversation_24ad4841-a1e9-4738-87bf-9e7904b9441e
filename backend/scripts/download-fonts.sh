#!/bin/bash

# 字体下载脚本
# 下载常用的开源中文字体到指定目录

set -e

# 默认字体目录
FONT_DIR="${1:-./fonts}"

echo "========================================="
echo "开始下载中文字体到目录: $FONT_DIR"
echo "========================================="

# 创建字体目录
mkdir -p "$FONT_DIR"

# 检查字体目录是否可写
if [ ! -w "$FONT_DIR" ]; then
    echo "错误: 字体目录不可写: $FONT_DIR"
    exit 1
fi

# 下载函数
download_font() {
    local font_name="$1"
    local font_url="$2"
    local font_file="$FONT_DIR/$font_name"
    
    if [ -f "$font_file" ]; then
        echo "字体已存在，跳过: $font_name"
        return 0
    fi
    
    echo "正在下载: $font_name"
    if command -v wget >/dev/null 2>&1; then
        wget -q --show-progress -O "$font_file" "$font_url"
    elif command -v curl >/dev/null 2>&1; then
        curl -L --progress-bar -o "$font_file" "$font_url"
    else
        echo "错误: 未找到wget或curl，无法下载字体"
        return 1
    fi
    
    # 验证下载的文件
    if [ -f "$font_file" ] && [ -s "$font_file" ]; then
        local file_size=$(stat -f%z "$font_file" 2>/dev/null || stat -c%s "$font_file" 2>/dev/null || echo "0")
        echo "✓ $font_name 下载成功 (${file_size} bytes)"
    else
        echo "✗ $font_name 下载失败"
        rm -f "$font_file"
        return 1
    fi
}

echo "开始下载开源中文字体..."

# Google Noto Sans CJK (思源黑体)
echo ""
echo "1. 下载 Google Noto Sans CJK..."
download_font "NotoSansCJK-Regular.ttc" \
    "https://github.com/googlefonts/noto-cjk/raw/main/Sans/OTC/NotoSansCJK-Regular.ttc"

# Adobe Source Han Sans (思源黑体)
echo ""
echo "2. 下载 Adobe Source Han Sans..."
download_font "SourceHanSans-Regular.ttc" \
    "https://github.com/adobe-fonts/source-han-sans/raw/release/OTC/SourceHanSans-Regular.ttc"

# Noto Sans SC (简体中文)
echo ""
echo "3. 下载 Noto Sans SC..."
download_font "NotoSansSC-Regular.ttf" \
    "https://github.com/googlefonts/noto-cjk/raw/main/Sans/SubsetOTF/SC/NotoSansSC-Regular.otf"

# Source Han Sans CN (思源黑体简体中文)
echo ""
echo "4. 下载 Source Han Sans CN..."
download_font "SourceHanSansCN-Regular.otf" \
    "https://github.com/adobe-fonts/source-han-sans/raw/release/SubsetOTF/SourceHanSansCN-Regular.otf"

echo ""
echo "========================================="
echo "字体下载完成！"
echo "========================================="

# 显示下载结果
echo "已下载的字体文件："
ls -la "$FONT_DIR"/*.{ttf,ttc,otf} 2>/dev/null || echo "未找到字体文件"

echo ""
echo "使用说明："
echo "1. 将字体目录设置为环境变量: export FONT_PATH=$FONT_DIR"
echo "2. 或者在启动应用时指定: java -Dfont.path=$FONT_DIR -jar app.jar"
echo "3. 或者在Docker中挂载: -v $FONT_DIR:/app/fonts"

echo ""
echo "注意事项："
echo "- 这些都是开源字体，可以免费使用"
echo "- 如果需要商业字体，请确保获得相应授权"
echo "- 字体文件较大，首次下载可能需要一些时间"
