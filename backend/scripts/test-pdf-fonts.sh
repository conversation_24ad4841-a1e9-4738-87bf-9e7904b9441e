#!/bin/bash

# PDF字体测试脚本
# 快速测试PDF生成和字体显示

set -e

# 配置
SERVER_URL="${1:-http://localhost:8080}"
OUTPUT_DIR="${2:-./test-output}"

echo "========================================="
echo "PDF字体测试脚本"
echo "服务器地址: $SERVER_URL"
echo "输出目录: $OUTPUT_DIR"
echo "========================================="

# 创建输出目录
mkdir -p "$OUTPUT_DIR"

# 检查服务器是否可访问
echo "1. 检查服务器连接..."
if ! curl -s --connect-timeout 5 "$SERVER_URL/test/fonts" > /dev/null; then
    echo "错误: 无法连接到服务器 $SERVER_URL"
    echo "请确保应用已启动并可访问"
    exit 1
fi
echo "✓ 服务器连接正常"

# 检查字体状态
echo ""
echo "2. 检查字体状态..."
FONT_STATUS=$(curl -s "$SERVER_URL/test/fonts")
echo "字体状态响应:"
echo "$FONT_STATUS" | python3 -m json.tool 2>/dev/null || echo "$FONT_STATUS"

# 提取字体数量
FONT_COUNT=$(echo "$FONT_STATUS" | grep -o '"fontCount":[0-9]*' | cut -d':' -f2 || echo "0")
echo ""
echo "已注册字体数量: $FONT_COUNT"

if [ "$FONT_COUNT" -eq 0 ]; then
    echo "⚠️  警告: 未注册任何字体，PDF可能无法正确显示中文"
    echo ""
    echo "建议操作："
    echo "1. 运行字体下载脚本: ./scripts/download-fonts.sh"
    echo "2. 设置字体路径环境变量: export FONT_PATH=./fonts"
    echo "3. 重启应用"
else
    echo "✓ 字体注册正常"
fi

# 生成字体测试PDF
echo ""
echo "3. 生成字体测试PDF..."
PDF_OUTPUT="$OUTPUT_DIR/font-test-$(date +%Y%m%d_%H%M%S).pdf"

if curl -s "$SERVER_URL/test/pdf-font-test" -o "$PDF_OUTPUT"; then
    if [ -f "$PDF_OUTPUT" ] && [ -s "$PDF_OUTPUT" ]; then
        FILE_SIZE=$(stat -f%z "$PDF_OUTPUT" 2>/dev/null || stat -c%s "$PDF_OUTPUT" 2>/dev/null || echo "0")
        echo "✓ 字体测试PDF生成成功: $PDF_OUTPUT (${FILE_SIZE} bytes)"
    else
        echo "✗ 字体测试PDF生成失败: 文件为空"
        exit 1
    fi
else
    echo "✗ 字体测试PDF生成失败: 请求失败"
    exit 1
fi

# 生成包含图片的PDF测试
echo ""
echo "4. 生成图片测试PDF..."
IMG_PDF_OUTPUT="$OUTPUT_DIR/image-test-$(date +%Y%m%d_%H%M%S).pdf"

if curl -s "$SERVER_URL/test/pdf" -o "$IMG_PDF_OUTPUT"; then
    if [ -f "$IMG_PDF_OUTPUT" ] && [ -s "$IMG_PDF_OUTPUT" ]; then
        FILE_SIZE=$(stat -f%z "$IMG_PDF_OUTPUT" 2>/dev/null || stat -c%s "$IMG_PDF_OUTPUT" 2>/dev/null || echo "0")
        echo "✓ 图片测试PDF生成成功: $IMG_PDF_OUTPUT (${FILE_SIZE} bytes)"
    else
        echo "✗ 图片测试PDF生成失败: 文件为空"
        exit 1
    fi
else
    echo "✗ 图片测试PDF生成失败: 请求失败"
    exit 1
fi

# 显示结果
echo ""
echo "========================================="
echo "测试完成！"
echo "========================================="
echo "生成的文件："
ls -la "$OUTPUT_DIR"/*.pdf 2>/dev/null || echo "未找到PDF文件"

echo ""
echo "检查建议："
echo "1. 打开生成的PDF文件，检查中文是否正确显示"
echo "2. 检查图片是否正确加载"
echo "3. 如果中文显示为方块，说明字体未正确加载"
echo "4. 如果图片不显示，检查网络连接和baseUrl设置"

echo ""
echo "故障排除："
echo "- 字体问题: 运行 ./scripts/download-fonts.sh 下载字体"
echo "- 图片问题: 检查服务器网络连接和防火墙设置"
echo "- 服务问题: 检查应用日志 docker logs <container-name>"

# 如果是macOS，尝试打开PDF
if command -v open >/dev/null 2>&1 && [ "$(uname)" = "Darwin" ]; then
    echo ""
    read -p "是否打开生成的PDF文件查看？(y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        open "$PDF_OUTPUT"
        open "$IMG_PDF_OUTPUT"
    fi
fi
