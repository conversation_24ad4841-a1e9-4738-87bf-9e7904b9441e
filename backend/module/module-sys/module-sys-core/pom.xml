<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>module</groupId>
        <artifactId>module-sys</artifactId>
        <version>${revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>module-sys-core</artifactId>
    <description>系统模块-core</description>

    <dependencies>
        <dependency>
            <groupId>infra</groupId>
            <artifactId>infra-domain</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>infra</groupId>
            <artifactId>infra-cache</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>infra</groupId>
            <artifactId>infra-auth</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>infra</groupId>
            <artifactId>infra-audit</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>infra</groupId>
            <artifactId>infra-oss</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>infra</groupId>
            <artifactId>infra-sms</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>infra</groupId>
            <artifactId>infra-task</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>jakarta.servlet</groupId>
            <artifactId>jakarta.servlet-api</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
            <scope>provided</scope>
        </dependency>
    </dependencies>

</project>