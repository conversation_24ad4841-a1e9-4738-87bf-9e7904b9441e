package module.sys.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import infra.domain.entity.IdEntity;
import infra.domain.model.TreeNode;
import infra.domain.service.FilterOptions;
import infra.domain.service.TreeService;
import lombok.extern.slf4j.Slf4j;
import module.sys.entity.Dept;
import module.sys.entity.Post;
import module.sys.mapper.DeptMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 部门服务
 */
@Slf4j
@Service
public class DeptService extends TreeService<DeptMapper, Dept> {
    @Autowired
    private PostService postService;

    @Override
    protected Function<QueryWrapper<Dept>, QueryWrapper<Dept>> bizFilter() {
        return wrapper -> wrapper.eq("disabled", false);
    }

    /**
     * 获取指定部门节点最近的公司节点
     * 从指定部门开始向上查找,返回找到的第一个类型为COMPANY的节点
     * 如果指定节点本身就是公司,则返回自身
     * 如果向上查找不到公司节点,则返回Optional.empty()
     *
     * @param deptId 部门ID
     * @return 找到的最近公司节点, 如果没找到则返回Optional.empty()
     */
    public Optional<Dept> getNearestCompany(Long deptId) {
        if (deptId == null) {
            return Optional.empty();
        }

        // 获取当前节点
        Optional<Dept> currentOpt = getById(deptId);
        if (currentOpt.isEmpty()) {
            return Optional.empty();
        }

        Dept current = currentOpt.get();

        // 如果当前节点就是公司,直接返回
        if (current.getType() == Dept.DeptType.COMPANY) {
            return Optional.of(current);
        }

        // 获取所有父节点(按路径从近到远排序)
        List<Dept> parents = getParents(deptId, false);

        // 按路径长度倒序排序,确保从近到远查找
        parents.sort((a, b) -> Integer.compare(b.getPath().length(), a.getPath().length()));

        // 查找第一个类型为COMPANY的节点
        return parents.stream()
                .filter(parent -> parent.getType() == Dept.DeptType.COMPANY)
                .findFirst();
    }

    /**
     * 获取组织结构树(含岗位)
     *
     * @param topId         顶级节点ID,为null时从根节点开始
     * @param includePost   是否包含岗位信息
     * @param filterOptions 业务过滤选项
     * @return 包含岗位信息的组织结构树
     */
    public List<TreeNode> getTreeWithPost(Long topId, boolean includePost, FilterOptions filterOptions) {
        // 获取部门列表
        List<Dept> depts;

        if (topId != null && topId > 0) {
            Optional<Dept> topNodeOpt = getById(topId, filterOptions);
            if (topNodeOpt.isEmpty()) {
                return Collections.emptyList();
            }

            Dept topNode = topNodeOpt.get();
            depts = getList(wrapper -> wrapper.likeRight("path", topNode.getPath()).ne("id", topId), filterOptions);
        } else {
            depts = getList(filterOptions);
        }

        if (depts.isEmpty()) {
            return Collections.emptyList();
        }

        // 获取岗位信息
        List<Post> posts = Collections.emptyList();
        if (includePost) {
            List<Long> deptIds = depts.stream().map(Dept::getId).collect(Collectors.toList());
            if (!deptIds.isEmpty()) {
                posts = postService.getList(wrapper -> wrapper.in("dept_id", deptIds), filterOptions);
            }
        }

        // 构建树
        return buildTreeWithPost(topId != null ? topId : 0L, depts, posts);
    }

    /**
     * 获取组织结构树(含岗位)
     */
    public List<TreeNode> getTreeWithPost(Long topId, boolean includePost) {
        return getTreeWithPost(topId, includePost, FilterOptions.DEFAULTS);
    }

    /**
     * 获取组织结构树(含岗位)
     */
    public List<TreeNode> getTreeWithPost(Long topId) {
        return getTreeWithPost(topId, true, FilterOptions.DEFAULTS);
    }

    /**
     * 构建包含岗位的树形结构
     */
    private List<TreeNode> buildTreeWithPost(Long parentId, List<Dept> depts, List<Post> posts) {
        // 按父节点ID分组
        Map<Long, List<Dept>> deptsByParent = depts.stream()
                .collect(Collectors.groupingBy(dept -> dept.getParentId() != null ? dept.getParentId() : 0L));

        // 按部门ID分组岗位
        Map<Long, List<Post>> postsByDept = posts.stream()
                .collect(Collectors.groupingBy(Post::getDeptId));

        return buildTreeRecursive(parentId, deptsByParent, postsByDept);
    }

    /**
     * 递归构建树结构
     * key中d开头的是部门，p开头的是岗位
     */
    private List<TreeNode> buildTreeRecursive(Long parentId,
                                              Map<Long, List<Dept>> deptsByParent,
                                              Map<Long, List<Post>> postsByDept) {
        List<Dept> childDepts = deptsByParent.getOrDefault(parentId, Collections.emptyList());

        // 排序
        childDepts.sort(Comparator.comparingInt((Dept a) -> a.getSort()).thenComparingLong(IdEntity::getId));

        List<TreeNode> result = new ArrayList<>();

        for (Dept dept : childDepts) {
            // 创建部门节点
            Map<String, Object> deptData = new HashMap<>();
            deptData.put("type", dept.getType());
            deptData.put("disabled", dept.getDisabled());

            TreeNode deptNode = TreeNode.builder()
                    .key("d" + dept.getId())
                    .id(dept.getId())
                    .name(dept.getName())
                    .path(dept.getPath())
                    .data(deptData)
                    .children(new ArrayList<>())
                    .build();

            // 递归添加子部门
            List<TreeNode> childNodes = buildTreeRecursive(dept.getId(), deptsByParent, postsByDept);
            if (!childNodes.isEmpty()) {
                deptNode.getChildren().addAll(childNodes);
            }

            // 添加岗位节点
            List<Post> deptPosts = postsByDept.getOrDefault(dept.getId(), Collections.emptyList());
            for (Post post : deptPosts) {
                Map<String, Object> postData = new HashMap<>();
                postData.put("type", 9); // 岗位类型标识
                postData.put("disabled", post.getDisabled());

                TreeNode postNode = TreeNode.builder()
                        .key("p" + post.getId())
                        .id(post.getId())
                        .name(post.getName())
                        .path(dept.getPath() + post.getId() + ",")
                        .data(postData)
                        .children(null)
                        .build();

                deptNode.getChildren().add(postNode);
            }

            // 如果没有子节点，设置children为null
            if (deptNode.getChildren().isEmpty()) {
                deptNode.setChildren(null);
            }

            result.add(deptNode);
        }

        return result;
    }
}
