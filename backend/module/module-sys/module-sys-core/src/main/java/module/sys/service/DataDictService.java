package module.sys.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import infra.core.exception.BizException;
import infra.core.text.Str;
import infra.domain.service.ServiceBase;
import lombok.extern.slf4j.Slf4j;
import module.sys.entity.DataDict;
import module.sys.entity.DataDictValue;
import module.sys.mapper.DataDictMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.function.Function;

/**
 * 数据字典服务
 */
@Slf4j
@Service
public class DataDictService extends ServiceBase<DataDictMapper, DataDict> {
    @Autowired
    private DataDictValueService dataDictValueService;

    @Override
    protected void validateEntity(DataDict entity, boolean isUpdate) {
        super.validateEntity(entity, isUpdate);

        if (existsByCode(entity.getCode(), isUpdate ? entity.getId() : null)) {
            throw new BizException("字典编码已存在");
        }
    }

    @Override
    protected Function<QueryWrapper<DataDict>, QueryWrapper<DataDict>> defaultOrderBy() {
        return wrapper -> wrapper.orderByAsc("sort")
                .orderByAsc("id");
    }

    /**
     * 检查字典编码是否已存在
     */
    public boolean existsByCode(String code, Long excludeId) {
        if (Str.isEmpty(code)) {
            return false;
        }

        return exists(wrapper -> {
            wrapper.eq("code", code);
            if (excludeId != null) {
                wrapper.ne("id", excludeId);
            }
        });
    }

    /**
     * 更新字典排序
     */
    @Transactional
    public void updateSort(Long dataId, Integer sort) {
        Optional<DataDict> dictOpt = getById(dataId);
        if (dictOpt.isPresent()) {
            DataDict dict = dictOpt.get();
            dict.setSort(sort);
            update(dict);
        }
    }

    /**
     * 获取指定字典下的所有字典值
     */
    public List<DataDictValue> getValues(Long dictId) {
        return dataDictValueService.getListByDict(dictId);
    }

    /**
     * 获取指定code字典下的所有字典值
     */
    public List<DataDictValue> getValuesByCode(String code) {
        Optional<DataDict> dictOpt = getByCode(code);
        if (dictOpt.isEmpty()) {
            throw new BizException("字典不存在");
        }

        return getValues(dictOpt.get().getId());
    }

    /**
     * 根据编码获取字典
     */
    public Optional<DataDict> getByCode(String code) {
        return getFirst(wrapper -> wrapper.eq("code", code));
    }
}
