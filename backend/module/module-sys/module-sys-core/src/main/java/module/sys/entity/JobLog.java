package module.sys.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import infra.domain.entity.IdEntity;
import lombok.*;

import java.io.Serial;
import java.time.LocalDateTime;

/**
 * 定时任务日志
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@TableName("sys_job_log")
public class JobLog extends IdEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    // 工作/任务id
    private String jobId;
    // 开始时间
    private LocalDateTime startTime;
    // 结束时间
    private LocalDateTime endTime;
    // 耗时(毫秒)
    private Long useTime;
    // 是否成功
    private Boolean success;
    // 执行消息(成功时可为返回结果/失败时可为错误信息)
    private String msg;
}
