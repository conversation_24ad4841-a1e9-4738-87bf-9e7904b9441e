package module.sys.model;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Data
public class SliderVerifyData implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 轨迹点列表
     */
    private List<TrackPoint> track;

    /**
     * 滑块位置
     */
    private Double position;

    /**
     * 拖动持续时间
     */
    private Long duration;

    @Data
    public static class TrackPoint implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * x坐标
         */
        private Double x;

        /**
         * y坐标
         */
        private Double y;

        /**
         * 时间戳
         */
        private Long t;
    }
}
