package module.sys.service;

import infra.domain.service.ServiceBase;
import lombok.extern.slf4j.Slf4j;
import module.sys.entity.ValidCode;
import module.sys.mapper.ValidCodeMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.util.Optional;

/**
 * 验证码服务
 */
@Slf4j
@Service
public class ValidCodeService extends ServiceBase<ValidCodeMapper, ValidCode> {
    private static final SecureRandom RANDOM = new SecureRandom();

    @Autowired
    private SettingService settingService;

    /**
     * 生成验证码
     *
     * @param extra 额外信息
     * @return 验证码记录ID
     */
    public Long generate(String extra) {
        String code = generateRandomCode();
        int expiryMinutes = settingService.getSafeSetting().getValidCodeExpiry();
        LocalDateTime expiry = LocalDateTime.now().plusMinutes(expiryMinutes);

        ValidCode validCode = new ValidCode();
        validCode.setCode(code);
        validCode.setExpiryTime(expiry);
        validCode.setExtra(extra);
        validCode.setUsed(false);
        validCode.setTryCount(0);

        add(validCode);
        return validCode.getId();
    }

    /**
     * 验证验证码
     *
     * @param dataId 验证码记录ID
     * @param code   验证码
     * @param extra  额外信息
     * @return 验证是否成功
     */
    public boolean verify(Long dataId, String code, String extra) {
        Optional<ValidCode> entityOpt = getById(dataId);
        if (entityOpt.isEmpty()) {
            return false;
        }

        ValidCode entity = entityOpt.get();

        if (entity.getUsed() || entity.getExpiryTime().isBefore(LocalDateTime.now())) {
            return false;
        }

        int maxTryCount = settingService.getSafeSetting().getValidCodeTryMax();
        if (entity.getTryCount() >= maxTryCount) {
            return false;
        }

        if (entity.getCode().equals(code) && (extra == null ? entity.getExtra() == null : extra.equals(entity.getExtra()))) {
            entity.setUsed(true);
            update(entity);
            return true;
        } else {
            entity.setTryCount(entity.getTryCount() + 1);
            update(entity);
            return false;
        }
    }

    /**
     * 清理已无效的验证码
     */
    public void clearInvalid() {
        deleteBatch(wrapper -> wrapper.lt("expiry_time", LocalDateTime.now()));
    }

    /**
     * 生成随机验证码
     */
    private String generateRandomCode() {
        StringBuilder code = new StringBuilder(6);
        for (int i = 0; i < 6; i++) {
            code.append(RANDOM.nextInt(10));
        }
        return code.toString();
    }
}
