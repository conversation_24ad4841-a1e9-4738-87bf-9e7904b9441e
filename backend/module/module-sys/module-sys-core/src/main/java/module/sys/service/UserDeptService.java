package module.sys.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import infra.auth.annotation.DataPerm;
import infra.domain.service.ServiceBase;
import lombok.extern.slf4j.Slf4j;
import module.sys.entity.Post;
import module.sys.entity.UserDept;
import module.sys.entity.UserPerm;
import module.sys.mapper.UserDeptMapper;
import module.sys.mapper.UserPermMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class UserDeptService extends ServiceBase<UserDeptMapper, UserDept> {
    // admin权限，admin权限以一个*代替
    public static final Set<String> ADMIN_PERMS = Set.of("*");

    @Autowired
    private PostService postService;
    @Autowired
    private UserPermMapper userPermMapper;

    /**
     * 获取用户的部门岗位关系列表
     *
     * @param userId    用户ID
     * @param effective 是否只返回当前有效的关系
     * @return 用户部门关系列表
     */
    public List<UserDept> getDepts(Long userId, boolean effective) {
        if (userId == null) {
            return Collections.emptyList();
        }

        return getList(wrapper -> {
            wrapper.eq("user_id", userId);

            if (effective) {
                LocalDateTime now = LocalDateTime.now();
                wrapper.le("start_time", now)
                        .and(w -> w.ge("end_time", now)
                                .or().isNull("end_time"));
            }
        });
    }

    /**
     * 获取用户的部门岗位关系列表（默认只返回有效关系）
     */
    public List<UserDept> getDepts(Long userId) {
        return getDepts(userId, true);
    }

    /**
     * 获取用户在指定部门的数据权限
     *
     * @param userId 用户ID
     * @param deptId 部门ID
     * @return 返回用户在该部门的最高数据权限级别，如果用户不属于该部门则返回null
     */
    public DataPerm getDeptDataPerm(Long userId, Long deptId) {
        if (userId == null || deptId == null) {
            return null;
        }

        List<UserDept> userDepts = getDepts(userId);
        List<Long> postIds = userDepts.stream()
                .filter(ud -> Objects.equals(ud.getDeptId(), deptId))
                .map(UserDept::getPostId)
                .toList();

        if (postIds.isEmpty()) {
            return null;
        }

        List<Post> posts = postService.getListByIds(postIds);
        if (posts.isEmpty()) {
            return null;
        }

        // 返回最高权限级别
        return posts.stream()
                .map(Post::getPerm)
                .filter(Objects::nonNull)
                .max(Comparator.comparing(DataPerm::ordinal))
                .orElse(null);
    }

    /**
     * 获取用户权限集
     *
     * @param userId 用户ID
     * @param deptId 部门ID，如果指定则只返回该部门下的权限
     * @return 用户权限集合
     */
    public Set<String> getPerms(Long userId, Long deptId) {
        if (userId == null) {
            return Collections.emptySet();
        }

        // 获取用户所有岗位权限
        List<UserDept> userDepts = getDepts(userId);
        if (deptId != null) {
            userDepts = userDepts.stream()
                    .filter(ud -> Objects.equals(ud.getDeptId(), deptId))
                    .toList();
        }

        List<Long> postIds = userDepts.stream()
                .map(UserDept::getPostId)
                .collect(Collectors.toList());

        Set<String> allPerms = new HashSet<>();
        if (!postIds.isEmpty()) {
            allPerms = postService.getPostsPerms(postIds);
        }

        // 获取用户特殊权限
        QueryWrapper<UserPerm> wrapper = new QueryWrapper<>();
        wrapper.eq("user_id", userId);
        List<UserPerm> specialPerms = userPermMapper.selectList(wrapper);

        for (UserPerm perm : specialPerms) {
            if (perm.getType() == UserPerm.UserPermType.ALLOW) {
                allPerms.add(perm.getPerm());
            } else { // DENY
                allPerms.remove(perm.getPerm());
            }
        }

        return allPerms;
    }

    /**
     * 获取用户权限集（不指定部门）
     */
    public Set<String> getPerms(Long userId) {
        return getPerms(userId, null);
    }

    /**
     * 根据部门ID获取用户部门关系
     *
     * @param deptIds 部门ID列表
     * @return 用户部门关系列表
     */
    public List<UserDept> getByDepts(Collection<Long> deptIds) {
        if (deptIds == null || deptIds.isEmpty()) {
            return Collections.emptyList();
        }

        return getList(wrapper -> {
            LocalDateTime now = LocalDateTime.now();
            wrapper.in("dept_id", deptIds)
                    .le("start_time", now)
                    .and(w -> w.ge("end_time", now).or().isNull("end_time"));
        });
    }

    /**
     * 根据岗位ID获取用户部门关系
     *
     * @param postIds 岗位ID列表
     * @return 用户部门关系列表
     */
    public List<UserDept> getByPosts(Collection<Long> postIds) {
        if (postIds == null || postIds.isEmpty()) {
            return Collections.emptyList();
        }

        return getList(wrapper -> {
            LocalDateTime now = LocalDateTime.now();
            wrapper.in("post_id", postIds)
                    .le("start_time", now)
                    .and(w -> w.ge("end_time", now).or().isNull("end_time"));
        });
    }
}
