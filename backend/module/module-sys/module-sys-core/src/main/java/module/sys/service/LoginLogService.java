package module.sys.service;

import infra.core.web.IpUtil;
import infra.core.web.ServletUtil;
import lombok.extern.slf4j.Slf4j;
import module.sys.entity.LoginLog;
import module.sys.mapper.LoginLogMapper;
import infra.domain.service.ServiceBase;
import infra.domain.query.PageParam;
import infra.domain.query.PageResult;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * 登录日志服务
 */
@Slf4j
@Service
public class LoginLogService extends ServiceBase<LoginLogMapper, LoginLog> {
    /**
     * 添加登录日志
     */
    public void logLogin(Long userId, String loginName, String userName, boolean success,
                         LoginLog.LoginType loginType, String error) {

        LoginLog loginLog = new LoginLog();
        loginLog.setUserId(userId);
        loginLog.setLoginName(loginName);
        loginLog.setUserName(userName);
        loginLog.setLoginTime(LocalDateTime.now());
        loginLog.setLoginType(loginType);
        loginLog.setSuccess(success);
        loginLog.setError(error);

        var request = ServletUtil.getRequest();
        if (request != null) {
            loginLog.setIp(IpUtil.getClientIp(request));
            loginLog.setClient(request.getHeader("User-Agent"));
        }
        add(loginLog);
    }

    /**
     * 按用户获取查询登录日志
     */
    public PageResult<LoginLog> getPageByUser(Long userId, PageParam pageParam) {
        return getPage(wrapper -> wrapper.eq("user_id", userId), pageParam);
    }

    /**
     * 获取用户最近n条登录记录
     */
    public List<LoginLog> getListByUser(Long userId, int limit) {
        return getList(wrapper ->
                        wrapper.eq("user_id", userId).orderByDesc("id"),
                limit
        );
    }

    /**
     * 清理日志
     */
    public void clearLogs(LocalDate beforeDate) {
        deleteBatch(wrapper -> wrapper.lt("login_time", beforeDate));
    }

    /**
     * 获取n分销内连续登录失败次数
     */
    public int getRecentFailedCount(Long userId, int lockMinutes) {
        // 获取最近一次成功登录记录
        Optional<LoginLog> lastSuccess = getFirst(wrapper ->
                wrapper.eq("user_id", userId)
                        .eq("success", true)
                        .orderByDesc("id"));

        LocalDateTime startTime = LocalDateTime.now().minusMinutes(lockMinutes);
        if (lastSuccess.isPresent() && lastSuccess.get().getLoginTime().isAfter(startTime)) {
            startTime = lastSuccess.get().getLoginTime();
        }

        LocalDateTime finalStartTime = startTime;
        return (int) count(wrapper ->
                wrapper.eq("user_id", userId)
                        .eq("success", false)
                        .gt("login_time", finalStartTime));
    }
}
