package module.sys.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import infra.core.exception.BizException;
import infra.domain.service.ServiceBase;
import infra.oss.core.IStorage;
import lombok.extern.slf4j.Slf4j;
import module.sys.entity.UpFile;
import module.sys.mapper.UpFileMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.io.OutputStream;
import java.time.LocalDateTime;
import java.util.Optional;
import java.util.UUID;
import java.util.function.Function;

/**
 * 文件上传服务
 */
@Slf4j
@Service
public class UpFileService extends ServiceBase<UpFileMapper, UpFile> {
    @Autowired
    private IStorage storage;

    @Override
    protected Function<QueryWrapper<UpFile>, QueryWrapper<UpFile>> bizFilter() {
        return wrapper -> wrapper.eq("deleted", false);
    }

    public UpFile uploadFile(MultipartFile uploadFile, String info) {
        boolean uploaded = false;
        String objectKey = "";
        try {
            String originalFilename = uploadFile.getOriginalFilename();
            if (originalFilename == null) {
                originalFilename = "未命名文件";
            }

            String fileExt = "";
            int lastDotIndex = originalFilename.lastIndexOf('.');
            if (lastDotIndex > 0) {
                fileExt = originalFilename.substring(lastDotIndex).toLowerCase();
            }

            String uniqueFilename = UUID.randomUUID().toString().replace("-", "") + fileExt;

            LocalDateTime now = LocalDateTime.now();
            objectKey = String.format("%d/%02d/%s", now.getYear(), now.getMonthValue(), uniqueFilename);

            storage.upload(uploadFile.getInputStream(), objectKey);
            String fullPath = storage.getUrl(objectKey);
            uploaded = true;

            UpFile upFile = new UpFile();
            upFile.setFileName(originalFilename);
            upFile.setFileExt(fileExt);
            upFile.setFileSize(uploadFile.getSize());
            upFile.setInfo(info);
            upFile.setObjectKey(objectKey);
            upFile.setFullPath(fullPath);

            add(upFile);
            return upFile;

        } catch (Exception e) {
            // 如果创建记录失败，删除已上传的文件
            if (uploaded) {
                try {
                    storage.delete(objectKey);
                } catch (Exception ignored) {
                }
            }
            throw new BizException("文件上传失败: " + e.getMessage());
        }
    }

    public void physicalDelete(Long fileId) {
        Optional<UpFile> fileOpt = getById(fileId);
        if (fileOpt.isEmpty()) {
            throw new BizException("文件不存在");
        }

        UpFile file = fileOpt.get();
        try {
            storage.delete(file.getObjectKey());
            deleteById(fileId);
        } catch (Exception e) {
            throw new BizException("文件删除失败: " + e.getMessage());
        }
    }

    /**
     * 下载到目标流
     */
    public void download(Long fileId, OutputStream destination) {
        Optional<UpFile> fileOpt = getById(fileId);
        if (fileOpt.isEmpty()) {
            throw new BizException("文件不存在");
        }

        UpFile file = fileOpt.get();
        storage.download(file.getObjectKey(), destination);
    }

    /**
     * 获取文件流
     */
    public FileStreamResult getFileStream(Long fileId) {
        Optional<UpFile> fileOpt = getById(fileId);
        if (fileOpt.isEmpty()) {
            throw new BizException("文件不存在");
        }

        UpFile file = fileOpt.get();
        try {
            InputStream inputStream = storage.getInputStream(file.getObjectKey());
            return new FileStreamResult(inputStream, file);
        } catch (Exception e) {
            throw new BizException("获取文件流失败: " + e.getMessage());
        }
    }

    /**
     * 文件流结果封装类
     */
    public record FileStreamResult(InputStream inputStream,
                                   UpFile fileInfo) implements AutoCloseable {

        @Override
        public void close() throws Exception {
            if (inputStream != null) {
                inputStream.close();
            }
        }
    }
}
