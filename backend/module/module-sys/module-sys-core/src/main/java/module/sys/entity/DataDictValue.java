package module.sys.entity;

import infra.domain.entity.EntityBase;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.io.Serial;

/**
 * 数据字典值
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@TableName("sys_data_dict_value")
public class DataDictValue extends EntityBase {
    @Serial
    private static final long serialVersionUID = 1L;

    // 字典Id
    private Long dictId;
    // 名称
    private String name;
    // 编号
    private String code;
    // 排序值
    @Builder.Default
    private Integer sort = 0;
    // 是否禁用
    @Builder.Default
    private Boolean disabled = false;
}
