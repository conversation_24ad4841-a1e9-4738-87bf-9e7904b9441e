package module.sys.entity;

import infra.core.common.IDictEnum;
import infra.domain.entity.EntityBase;
import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.io.Serial;
import java.time.LocalDateTime;

/**
 * 用户
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@TableName("sys_user")
public class User extends EntityBase {
    @Serial
    private static final long serialVersionUID = 1L;

    // 登录名称
    private String loginName;
    // 用户名称
    private String userName;
    // 真实姓名全拼
    private String pinyin;
    // 真实姓名简拼
    private String py;
    // 密码
    private String password;
    // 手机号码
    private String phone;
    // 邮箱
    private String email;
    // 是否管理员
    @Builder.Default
    private Boolean admin = false;
    // 用户状态
    @Builder.Default
    private UserStatus status = UserStatus.NORMAL;
    // 最后登录时间
    private LocalDateTime lastLogin;

    /**
     * 用户状态枚举
     */
    @Getter
    @AllArgsConstructor
    public enum UserStatus implements IDictEnum {
        NORMAL(0, "正常"),
        DISABLED(-1, "禁用");

        @EnumValue
        private final int value;
        private final String desc;
    }
}
