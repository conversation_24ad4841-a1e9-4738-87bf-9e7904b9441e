package module.sys.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class AppSetting implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 应用名称
     */
    private String appName = "管理系统";
    /**
     * 是否启用水印
     */
    private boolean watermark = false;
}
