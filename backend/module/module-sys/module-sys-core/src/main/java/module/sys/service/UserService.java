package module.sys.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import infra.core.exception.BizException;
import infra.core.text.Chinese;
import infra.core.text.Str;
import infra.domain.service.ServiceBase;
import lombok.extern.slf4j.Slf4j;
import module.sys.entity.Dept;
import module.sys.entity.User;
import module.sys.entity.UserDept;
import module.sys.entity.UserPerm;
import module.sys.mapper.UserMapper;
import module.sys.mapper.UserPermMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;

/**
 * 用户服务
 */
@Slf4j
@Service
public class UserService extends ServiceBase<UserMapper, User> {
    @Autowired
    private UserPermMapper userPermMapper;
    @Autowired
    private DeptService deptService;
    @Autowired
    private UserDeptService userDeptService;

    @Override
    protected Function<QueryWrapper<User>, QueryWrapper<User>> bizFilter() {
        return wrapper -> wrapper.ne("status", User.UserStatus.DISABLED);
    }

    @Override
    protected void validateEntity(User entity, boolean isUpdate) {
        super.validateEntity(entity, isUpdate);

        if (existsUser(entity.getLoginName(), isUpdate ? entity.getId() : null)) {
            throw new BizException("登录名已存在");
        }
    }

    /**
     * 更新拼音字段
     */
    private void updatePinyin(User user) {
        if (Str.isEmpty(user.getUserName())) {
            return;
        }

        Chinese.Pinyin pinyin = Chinese.getPinyin(user.getUserName());
        user.setPinyin(pinyin.pinyin());
        user.setPy(pinyin.py());
    }

    @Override
    public boolean add(User user) {
        if (user == null) {
            throw new IllegalArgumentException("新增数据不能为空");
        }

        updatePinyin(user);
        return super.add(user);
    }

    @Override
    public boolean update(User user) {
        if (user == null) {
            throw new IllegalArgumentException("更新数据不能为空");
        }

        updatePinyin(user);
        return super.update(user);
    }

    /**
     * 根据用户名获取用户
     */
    public Optional<User> getByLoginName(String loginName) {
        if (Str.isEmpty(loginName)) {
            return Optional.empty();
        }
        return getFirst(wrapper -> wrapper.eq("login_name", loginName));
    }

    /**
     * 根据手机号获取用户
     */
    public Optional<User> getByPhone(String phone) {
        if (Str.isEmpty(phone)) {
            return Optional.empty();
        }
        return getFirst(wrapper -> wrapper.eq("phone", phone));
    }

    /**
     * 获取安全扩展字符
     */
    public static String getSafeExtra(User user) {
        if (user == null) return "";

        String disabledStatus = user.getStatus().getValue() + "_";
        if (Str.isEmpty(user.getPassword())) {
            return disabledStatus;
        }
        return disabledStatus + user.getPassword().substring(user.getPassword().length() - 8);
    }

    /**
     * 是否存在指定的用户
     */
    public boolean existsUser(String loginName, Long excludeId) {
        if (Str.isEmpty(loginName)) {
            return false;
        }

        return exists(wrapper -> {
            wrapper.eq("login_name", loginName);
            wrapper.ne(excludeId != null,"id", excludeId);
        });
    }

    /**
     * 获取用户特殊权限
     */
    public List<UserPerm> getSpecialPerms(Long userId) {
        if (userId == null) {
            return Collections.emptyList();
        }

        QueryWrapper<UserPerm> wrapper = new QueryWrapper<>();
        wrapper.eq("user_id", userId);
        return userPermMapper.selectList(wrapper);
    }

    /**
     * 更新用户特殊权限
     */
    @Transactional
    public void updateSpecialPerms(Long userId, Set<String> allowPerms, Set<String> denyPerms) {
        if (userId == null) {
            throw new IllegalArgumentException("用户ID不能为空");
        }

        Set<String> allowSet = allowPerms != null ? new HashSet<>(allowPerms) : new HashSet<>();
        Set<String> denySet = denyPerms != null ? new HashSet<>(denyPerms) : new HashSet<>();

        // 检查是否有重复的权限
        Set<String> intersection = new HashSet<>(allowSet);
        intersection.retainAll(denySet);
        if (!intersection.isEmpty()) {
            throw new BizException("允许权限和拒绝权限不能冲突");
        }

        // 删除原有特殊权限
        QueryWrapper<UserPerm> deleteWrapper = new QueryWrapper<>();
        deleteWrapper.eq("user_id", userId);
        userPermMapper.delete(deleteWrapper);

        // 批量创建新权限
        List<UserPerm> newPerms = new ArrayList<>();

        // 添加允许权限
        for (String perm : allowSet) {
            UserPerm userPerm = new UserPerm();
            userPerm.setUserId(userId);
            userPerm.setPerm(perm);
            userPerm.setType(UserPerm.UserPermType.ALLOW);
            newPerms.add(userPerm);
        }

        // 添加拒绝权限
        for (String perm : denySet) {
            UserPerm userPerm = new UserPerm();
            userPerm.setUserId(userId);
            userPerm.setPerm(perm);
            userPerm.setType(UserPerm.UserPermType.DENY);
            newPerms.add(userPerm);
        }

        // 批量插入
        if (!newPerms.isEmpty()) {
            for (UserPerm perm : newPerms) {
                userPermMapper.insert(perm);
            }
        }
    }

    /**
     * 获取用户特殊权限按类型分组
     */
    public Map<UserPerm.UserPermType, Set<String>> getSpecialPermsGrouped(Long userId) {
        List<UserPerm> perms = getSpecialPerms(userId);

        Map<UserPerm.UserPermType, Set<String>> result = new HashMap<>();
        result.put(UserPerm.UserPermType.ALLOW, new HashSet<>());
        result.put(UserPerm.UserPermType.DENY, new HashSet<>());

        for (UserPerm perm : perms) {
            result.get(perm.getType()).add(perm.getPerm());
        }

        return result;
    }

    /**
     * 获取用户列表
     */
    public List<User> getList(Long dept, boolean children) {
        if (dept != null) {
            List<Long> deptIds;
            if (children) {
                deptIds = deptService.getAllChildren(dept, true).stream().map(Dept::getId).toList();
            } else {
                deptIds = List.of(dept);
            }

            if (deptIds.isEmpty()) {
                return Collections.emptyList();
            }

            var userIds = userDeptService.getByDepts(deptIds).stream()
                    .map(UserDept::getUserId)
                    .toList();

            if (userIds.isEmpty()) {
                return Collections.emptyList();
            }

            return getListByIds(userIds);
        } else {
            return getList();
        }
    }
}
