package module.sys.service;

import infra.auth.annotation.DataPerm;
import infra.auth.config.AuthConfigProperties;
import infra.auth.core.IUser;
import infra.cache.core.ICache;
import infra.cache.impl.NoneCache;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import module.sys.entity.Dept;
import module.sys.model.AuthUser;

import java.util.*;

/**
 * 认证用户
 */
@Slf4j
@AllArgsConstructor
public class AuthUserImpl implements IUser {
    // 用户信息
    private final AuthUser userModel;
    // 部门服务
    private final DeptService deptService;
    // 用户部门服务
    private final UserDeptService userDeptService;
    // 岗位服务
    private final PostService postService;
    // 缓存
    private final ICache cache;
    // 配置
    private final AuthConfigProperties config;

    @Override
    public Long getId() {
        return userModel.getId();
    }

    @Override
    public String getLoginName() {
        return userModel.getLoginName();
    }

    @Override
    public String getUserName() {
        return userModel.getUserName();
    }

    @Override
    public Long getDeptId() {
        return userModel.getDeptId();
    }

    @Override
    public String getDeptName() {
        return userModel.getDeptName();
    }

    /**
     * 获取部门及子部门ID
     */
    @Override
    @SuppressWarnings("unchecked")
    public List<Long> getDeptAndChildren() {
        if (userModel.getDeptId() == null) {
            return Collections.emptyList();
        }

        String cacheKey = getAuthCachePrefix(userModel.getId()) + "deptChildren" + userModel.getDeptId();
        return (List<Long>) getCache().getOrSet(cacheKey, List.class, () ->
                        deptService.getAllChildren(userModel.getDeptId(), true)
                                .stream()
                                .map(Dept::getId)
                                .toList()
                , config.getUserCacheExpireSeconds());
    }

    /**
     * 获取数据权限
     */
    @Override
    public DataPerm getDataPerm() {
        if (isAdmin()) {
            return DataPerm.ALL;
        }

        if (userModel.getDeptId() == null) {
            return DataPerm.SELF;
        }

        String cacheKey = getAuthCachePrefix(userModel.getId()) + "dataPerm" + userModel.getDeptId();
        return getCache().getOrSet(cacheKey, DataPerm.class, () -> {
            DataPerm dataPerm = userDeptService.getDeptDataPerm(userModel.getId(), userModel.getDeptId());
            if (dataPerm == null) {
                dataPerm = DataPerm.SELF;
            }
            return dataPerm;
        }, config.getUserCacheExpireSeconds());
    }

    /**
     * 获取用户权限
     */
    @Override
    @SuppressWarnings("unchecked")
    public Set<String> getPerms() {
        if (isAdmin()) {
            return UserDeptService.ADMIN_PERMS;
        }

        String cacheKey = getAuthCachePrefix(userModel.getId()) + "perms" + userModel.getDeptId();
        return (Set<String>) getCache().getOrSet(cacheKey, Set.class, () ->
                userDeptService.getPerms(userModel.getId(), userModel.getDeptId()), config.getUserCacheExpireSeconds());
    }

    /**
     * 是否为管理员
     */
    @Override
    public boolean isAdmin() {
        return userModel.getAdmin() != null && userModel.getAdmin();
    }

    /**
     * 获取授权缓存前缀，与AuthService中的一致
     */
    public String getAuthCachePrefix(Long userId) {
        return config.getUserCachePrefix() + userId + ":";
    }

    /**
     * 获取缓存
     */
    private ICache getCache() {
        return cache == null ? NoneCache.INSTANCE : cache;
    }
}
