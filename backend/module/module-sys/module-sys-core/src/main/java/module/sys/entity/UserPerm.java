package module.sys.entity;

import infra.domain.entity.EntityBase;
import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.io.Serial;

/**
 * 用户特殊权限配置
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@TableName("sys_user_perm")
public class UserPerm extends EntityBase {
    @Serial
    private static final long serialVersionUID = 1L;

    // 用户Id
    private Long userId;
    // 授权类型
    @Builder.Default
    private UserPermType type = UserPermType.ALLOW;
    // 权限
    private String perm;

    /**
     * 用户特殊权限配置类型枚举
     */
    @Getter
    @AllArgsConstructor
    public enum UserPermType {
        ALLOW(0, "允许"),
        DENY(1, "拒绝");

        @EnumValue
        private final Integer value;
        private final String desc;
    }
}
