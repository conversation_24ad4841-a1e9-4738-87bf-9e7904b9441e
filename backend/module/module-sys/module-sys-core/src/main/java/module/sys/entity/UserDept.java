package module.sys.entity;

import infra.domain.entity.EntityBase;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.io.Serial;
import java.time.LocalDateTime;

/**
 * 用户部门
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@TableName("sys_user_dept")
public class UserDept extends EntityBase {
    @Serial
    private static final long serialVersionUID = 1L;

    // 用户Id
    private Long userId;
    // 部门Id
    private Long deptId;
    // 岗位Id
    private Long postId;
    // 是否为主部门
    @Builder.Default
    private Boolean main = true;
    // 开始时间
    private LocalDateTime startTime;
    // 结束时间
    private LocalDateTime endTime;
}
