package module.sys.service;

import infra.cache.core.ICache;
import infra.core.security.Md5;
import infra.core.text.JSON;
import lombok.extern.slf4j.Slf4j;
import module.sys.model.SliderVerifyData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 滑块验证服务
 */
@Slf4j
@Service
public class SliderVerifyService {
    private static final String CACHE_PREFIX = "slider_verify:";
    private static final int CACHE_MINUTES = 10;

    @Autowired
    private ICache cache;

    /**
     * 验证行为数据
     *
     * @param data 行为数据
     * @return 是否通过验证
     */
    public boolean verify(SliderVerifyData data) {
        if (data == null || data.getTrack() == null) {
            return false;
        }

        // 1. 检查轨迹点数量
        if (data.getTrack().size() < 10) {
            log.info("轨迹点数量不足");
            return false;
        }

        // 2. 计算速度变化
        List<Double> speeds = new ArrayList<>();
        List<SliderVerifyData.TrackPoint> track = data.getTrack();

        for (int i = 1; i < track.size(); i++) {
            long timeDiff = track.get(i).getT() - track.get(i - 1).getT();
            if (timeDiff <= 0) {
                log.info("时间差异异常");
                return false;
            }

            double distance = track.get(i).getX() - track.get(i - 1).getX();
            speeds.add(distance / timeDiff);
        }

        // 3. 检查速度变化是否自然
        if (speeds.size() < 3) {
            log.info("速度变化数量不足");
            return false;
        }

        List<Double> speedChanges = new ArrayList<>();
        for (int i = 1; i < speeds.size(); i++) {
            speedChanges.add(speeds.get(i) - speeds.get(i - 1));
        }

        double maxSpeedChange = speedChanges.stream().mapToDouble(Double::doubleValue).max().orElse(0);
        double minSpeedChange = speedChanges.stream().mapToDouble(Double::doubleValue).min().orElse(0);

        if (maxSpeedChange - minSpeedChange < 0.0001) {
            log.info("速度变化一致，可能是机器人");
            return false;
        }

        // 4. 检查y轴波动
        double maxY = track.stream().mapToDouble(SliderVerifyData.TrackPoint::getY).max().orElse(0);
        double minY = track.stream().mapToDouble(SliderVerifyData.TrackPoint::getY).min().orElse(0);
        double yVariance = maxY - minY;

        if (yVariance < 0.001) {
            log.info("y轴几乎没有波动，可能是机器人");
            return false;
        }

        // 5. 检查时间间隔的一致性
        List<Long> timeDiffs = new ArrayList<>();
        for (int i = 1; i < track.size(); i++) {
            timeDiffs.add(track.get(i).getT() - track.get(i - 1).getT());
        }

        double avgTimeDiff = timeDiffs.stream().mapToLong(Long::longValue).average().orElse(0);
        double timeDiffVariance = timeDiffs.stream()
                .mapToDouble(t -> Math.pow(t - avgTimeDiff, 2))
                .average().orElse(0);

        if (timeDiffVariance < 10) {
            log.info("时间间隔过于规律，可能是机器人");
            return false;
        }

        return cacheVerify(data);
    }

    /**
     * 验证并缓存，如果行为数据已存在，则验证失败，否则缓存行为数据
     *
     * @param data 行为数据
     * @return 验证结果
     */
    private boolean cacheVerify(SliderVerifyData data) {
        try {
            // 将行为数据序列化为JSON字符串
            List<Map<String, Object>> trackData = new ArrayList<>();
            for (SliderVerifyData.TrackPoint point : data.getTrack()) {
                Map<String, Object> pointMap = new HashMap<>();
                pointMap.put("x", point.getX());
                pointMap.put("y", point.getY());
                pointMap.put("t", point.getT());
                trackData.add(pointMap);
            }

            String trackJson = JSON.toJson(trackData);
            String hashInput = trackJson + ":" + data.getPosition() + ":" + data.getDuration();

            // 计算哈希值
            String trackHash = Md5.hashString(hashInput);

            // 缓存判断与存储
            String cacheKey = CACHE_PREFIX + trackHash;
            if (cache.exists(cacheKey)) {
                return false;
            }

            cache.set(cacheKey, "1", CACHE_MINUTES * 60);
            return true;

        } catch (Exception e) {
            log.error("缓存验证失败", e);
            return false;
        }
    }
}
