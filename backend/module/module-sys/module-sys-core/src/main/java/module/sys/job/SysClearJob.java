package module.sys.job;

import infra.task.core.IJob;
import lombok.extern.slf4j.Slf4j;
import module.sys.service.ValidCodeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 系统模块定量清理服务
 */
@Slf4j
@Component
public class SysClear<PERSON>ob implements IJob {
    @Autowired
    private ValidCodeService validCodeService;

    @Override
    public String getJobId() {
        return "sys_clear";
    }

    @Override
    public String getJobDesc() {
        return "系统模块-清理过期数据";
    }

    @Override
    public String getCron() {
        return "0 0 3 5,15,25 * *";
    }

    @Override
    public String getCronDesc() {
        return "每5号,15号,25号凌晨3:00执行";
    }

    @Override
    public Integer getSort() {
        return 2;
    }

    @Override
    public String execute() throws Exception {
        validCodeService.clearInvalid();
        log.info("已清理过期验证码");
        return "已清理过期验证码";
    }
}
