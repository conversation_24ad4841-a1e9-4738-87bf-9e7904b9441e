package module.sys.config;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.validation.annotation.Validated;

/**
 * 系统模管理配置
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Validated
@Configuration
@ConfigurationProperties(prefix = "app.module.sys")
public class SysConfigProperties {
    private String defaultPassword;
}
