package module.sys.entity;

import infra.domain.entity.EntityBase;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.io.Serial;

/**
 * 文件
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@TableName("sys_upfile")
public class UpFile extends EntityBase {
    @Serial
    private static final long serialVersionUID = 1L;

    // 文件名
    private String fileName;
    // 存储key(本地时是路径, 云存储时是object_key)
    private String objectKey;
    // 完整访问路径
    private String fullPath;
    // 文件扩展名
    private String fileExt;
    // 文件大小(字节)
    private Long fileSize;
    // 文件描述
    private String info;
    // 已删除
    @Builder.Default
    private Boolean deleted = false;
}
