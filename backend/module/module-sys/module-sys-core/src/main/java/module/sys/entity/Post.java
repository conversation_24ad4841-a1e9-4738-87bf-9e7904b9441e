package module.sys.entity;

import infra.auth.annotation.DataPerm;
import infra.domain.entity.EntityBase;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.io.Serial;

/**
 * 岗位
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@TableName("sys_post")
public class Post extends EntityBase {
    @Serial
    private static final long serialVersionUID = 1L;

    // 所属部门Id
    private Long deptId;
    // 岗位编码
    private String code;
    // 岗位名称
    private String name;
    // 数据权限
    @Builder.Default
    private DataPerm perm = DataPerm.SELF;
    // 排序值
    @Builder.Default
    private Integer sort = 0;
    // 是否禁用
    @Builder.Default
    private Boolean disabled = false;
}
