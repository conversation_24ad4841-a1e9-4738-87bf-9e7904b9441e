package module.sys.admin.controller;

import infra.auth.annotation.DataPerm;
import infra.auth.annotation.Perm;
import infra.core.common.IDictEnum;
import infra.core.common.Result;
import infra.core.security.Aes;
import infra.core.security.Sm4;
import infra.domain.encrypt.IFieldEncryptor;
import infra.domain.model.TreeNode;
import infra.domain.service.FilterOptions;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import module.sys.admin.dto.CommonDto;
import module.sys.entity.Job;
import module.sys.entity.User;
import module.sys.service.*;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "公共数据获取")
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/sys/common")
public class CommonController {

    private final SettingService settingService;
    private final DeptService deptService;
    private final UserService userService;
    private final UserDeptService userDeptService;
    private final ValidCodeService validCodeService;
    private final IFieldEncryptor encryptor;

    @GetMapping("/test")
    public Object test(Long id) {
        return "";
    }

    @GetMapping("/key")
    public String key(String type) {
        if ("sm4".equals(type)) {
            return Sm4.generateKey();
        } else {
            return Aes.generateKey();
        }
    }

    @Operation(summary = "获取系统设置")
    @GetMapping("/setting")
    public Result<CommonDto.AppInfoDto> getSetting() {
        var settings = settingService.getAppSetting();
        return Result.okData(new CommonDto.AppInfoDto(settings.getAppName(), settings.isWatermark()));
    }

    @Operation(summary = "获取状态字典")
    @GetMapping("/status")
    @Perm
    public Result<List<IDictEnum.DictEnumItem>> getStatus(@RequestParam String key) {
        return switch (key) {
            case "job_status" -> Result.okData(IDictEnum.getItems(Job.JobStatus.class));
            case "data_perm" -> Result.okData(IDictEnum.getItems(DataPerm.class));
            case "user_status" -> Result.okData(IDictEnum.getItems(User.UserStatus.class));
            default -> Result.fail("不支持的类型");
        };
    }

    @Operation(summary = "获取组织结构树")
    @GetMapping("/dept-tree")
    @Perm
    public Result<List<TreeNode>> getDeptTree(
            @RequestParam(required = false) Long parent,
            @RequestParam(defaultValue = "false") boolean post,
            @RequestParam(defaultValue = "false") boolean disabled) {

        var tree = deptService.getTreeWithPost(parent, post, FilterOptions.DEFAULTS);
        return Result.okData(tree);
    }

    @Operation(summary = "获取用户数据")
    @GetMapping("/users")
    @Perm
    public Result<List<CommonDto.UserInfoDto>> getUsers(
            @RequestParam(required = false) Long dept,
            @RequestParam(defaultValue = "false") boolean children) {

        List<User> users = userService.getList(dept, children);
        return Result.okData(users.stream().map(user -> new CommonDto.UserInfoDto(
                user.getId(),
                user.getUserName(),
                user.getPinyin(),
                user.getPy(),
                user.getPhone(),
                user.getEmail()
        )).toList());
    }
}
