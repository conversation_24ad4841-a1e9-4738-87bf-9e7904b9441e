package module.sys.admin.controller;

import infra.audit.core.AuditLog;
import infra.auth.annotation.Perm;
import infra.core.common.Result;
import infra.core.text.Str;
import infra.core.web.ServletUtil;
import infra.domain.query.PageParam;
import infra.domain.query.PageResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import module.sys.admin.dto.UpFileDto;
import module.sys.entity.UpFile;
import module.sys.service.UpFileService;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@Tag(name = "文件管理")
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/sys/upfile")
public class UpfileController {

    private final UpFileService upFileService;

    @Operation(summary = "文件管理列表")
    @GetMapping("/list")
    @Perm("sys:upfile")
    public Result<PageResult<UpFile>> list(
            String type,
            String key,
            LocalDate startTime,
            LocalDate endTime,
            PageParam pageParam) {

        var result = upFileService.getPage(wrapper -> {
            if (!Str.isEmpty(key)) {
                String keyTrim = key.trim();
                wrapper.like("fileName".equals(type), "file_name", keyTrim);
                wrapper.like("info".equals(type), "info", keyTrim);
                wrapper.like("fileExt".equals(type), "file_ext", keyTrim);
                wrapper.like("createByName".equals(type), "create_by_name", keyTrim);
            }

            wrapper.ge(startTime != null, "create_time", startTime);
            wrapper.le(endTime != null, "create_time", endTime);
        }, pageParam);

        return Result.okData(result);
    }

    @Operation(summary = "多文件上传")
    @PostMapping("/upload")
    @AuditLog(code = "sys:upfile:upload", value = "文件管理-上传")
    @Perm("sys:upfile:upload")
    public Result<List<UpFileDto.UploadResult>> upload(
            @RequestParam("files") List<MultipartFile> files,
            @RequestParam(value = "infos", required = false) List<String> infos) {

        List<UpFileDto.UploadResult> results = new ArrayList<>();

        for (int i = 0; i < files.size(); i++) {
            MultipartFile file = files.get(i);
            String info = (infos != null && i < infos.size()) ? infos.get(i) : "";

            UpFile upfile = upFileService.uploadFile(file, info);
            results.add(UpFileDto.UploadResult.builder()
                    .id(upfile.getId())
                    .fileName(upfile.getFileName())
                    .objectKey(upfile.getObjectKey())
                    .fullPath(upfile.getFullPath())
                    .build());
        }

        return Result.okData(results);
    }

    @Operation(summary = "更新文件描述")
    @PostMapping("/update-info")
    @AuditLog(code = "sys:upfile:updateInfo", value = "文件管理-更新描述")
    @Perm("sys:upfile:updateInfo")
    public Result<Void> updateInfo(@RequestBody @Valid List<UpFileDto.UpdateInfoDto> data) {
        for (UpFileDto.UpdateInfoDto item : data) {
            var upfileOpt = upFileService.getById(item.getId());
            if (upfileOpt.isEmpty()) continue;

            var upfile = upfileOpt.get();
            if (upfile.getInfo().equals(item.getInfo())) {
                upfile.setInfo(item.getInfo());
                upFileService.update(upfile);
            }
        }
        return Result.OK;
    }

    @Operation(summary = "删除文件")
    @PostMapping("/delete")
    @AuditLog(code = "sys:upfile:delete", value = "文件管理-删除")
    @Perm("sys:upfile:delete")
    public Result<Void> delete(@RequestBody List<Long> ids) {
        if (ids != null && !ids.isEmpty()) {
            upFileService.deleteByIds(ids);
        }
        return Result.OK;
    }

    @Operation(summary = "物理删除文件")
    @PostMapping("/delete-physical")
    @AuditLog(code = "sys:upfile:deletePhysical", value = "文件管理-物理删除")
    @Perm("sys:upfile:deletePhysical")
    public Result<Void> deletePhysical(@RequestBody List<Long> ids) {
        if (ids != null && !ids.isEmpty()) {
            for (Long fileId : ids) {
                upFileService.physicalDelete(fileId);
            }
        }
        return Result.OK;
    }

    @Operation(summary = "文件下载")
    @PostMapping("/download")
    @AuditLog(code = "sys:upfile:download", value = "文件管理-下载")
    @Perm("sys:upfile:download")
    public void download(@RequestBody List<Long> ids, HttpServletResponse response) throws IOException {
        try {
            if (ids == null || ids.size() != 1) {
                ServletUtil.writeJson(response, Result.fail("请选择单个文件进行下载"));
                return;
            }

            try (UpFileService.FileStreamResult fileStreamResult = upFileService.getFileStream(ids.getFirst())) {
                try (ServletOutputStream outputStream = ServletUtil.writeFile(response,
                        fileStreamResult.fileInfo().getFileName(),
                        "application/octet-stream"
                )) {
                    fileStreamResult.inputStream().transferTo(outputStream);
                }
            }
        } catch (Exception ex) {
            ServletUtil.writeJson(response, Result.fail("文件下载失败"));
        }
    }

    @Operation(summary = "查看文件")
    @GetMapping("/view")
    @Perm("sys:upfile:view")
    public Result<String> view(Long fid) {
        var upfile = upFileService.getById(fid);
        return upfile.map(upFile ->
                Result.okData(upFile.getFullPath()))
                .orElseGet(() -> Result.fail("文件不存在"));
    }
}
