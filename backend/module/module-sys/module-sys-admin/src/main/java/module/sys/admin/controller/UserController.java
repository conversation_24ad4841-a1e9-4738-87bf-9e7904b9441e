package module.sys.admin.controller;

import infra.audit.core.AuditLog;
import infra.auth.annotation.Perm;
import infra.core.common.CallUtil;
import infra.core.common.ObjectUtil;
import infra.core.common.Result;
import infra.core.exception.BizException;
import infra.core.text.Str;
import infra.domain.query.PageParam;
import infra.domain.query.PageResult;
import infra.domain.service.FilterOptions;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import module.sys.admin.dto.UserDto;
import module.sys.config.SysConfigProperties;
import module.sys.entity.User;
import module.sys.entity.UserDept;
import module.sys.entity.UserPerm;
import module.sys.service.*;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.*;

@Tag(name = "组织结构-用户")
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/sys/user")
public class UserController {

    private final UserService userService;
    private final DeptService deptService;
    private final PostService postService;
    private final UserDeptService userDeptService;
    private final AuthUserService authUserService;
    private final SysConfigProperties config;

    @Operation(summary = "用户列表")
    @GetMapping("/list")
    @Perm("sys:dept")
    public Result<PageResult<User>> list(
            String type,
            String key,
            Long deptId,
            Long postId,
            User.UserStatus status,
            PageParam pageParam) {

        // 根据部门或岗位过滤用户ID
        List<Long> userIds;
        if (deptId != null) {
            List<Long> deptIds = deptService.getAllChildrenIds(deptId, true, FilterOptions.DISABLE_ALL_FILTER);
            if (!deptIds.isEmpty()) {
                userIds = userDeptService.getByDepts(deptIds)
                        .stream()
                        .map(UserDept::getUserId)
                        .toList();
            } else {
                userIds = null;
            }
        } else if (postId != null) {
            userIds = userDeptService.getByPosts(List.of(postId))
                    .stream()
                    .map(UserDept::getUserId)
                    .toList();
        } else {
            userIds = null;
        }

        var result = userService.getPage(wrapper -> {
            if (!Str.isEmpty(key)) {
                String keyTrim = key.trim();
                if ("name".equals(type)) {
                    wrapper.and(w -> w.like("user_name", keyTrim)
                            .or().like("login_name", keyTrim)
                            .or().like("pinyin", keyTrim)
                            .or().like("py", keyTrim)
                            .or().like("phone", keyTrim)
                            .or().like("email", keyTrim));
                }
            }
            wrapper.in(userIds != null, "id", userIds);
            wrapper.eq(status != null, "status", status);
        }, pageParam, FilterOptions.DISABLE_ALL_FILTER);

        return Result.okData(result);
    }

    @Operation(summary = "新增用户")
    @PostMapping("/add")
    @AuditLog(code = "sys:dept:addUser", value = "组织结构-新增用户")
    @Perm("sys:dept:addUser")
    @Transactional
    public Result<Long> add(@RequestBody @Valid UserDto.EditDto dto) {
        User entity = User.builder()
                .loginName(dto.getLoginName())
                .userName(dto.getUserName())
                .phone(dto.getPhone())
                .email(dto.getEmail())
                .status(dto.getStatus())
                .build();

        if (!Str.isEmpty(dto.getPassword())) {
            entity.setPassword(authUserService.hashPassword(dto.getPassword()));
        } else if (!Str.isEmpty(config.getDefaultPassword())) {
            entity.setPassword(authUserService.hashPassword(dto.getPassword()));
        }
        if (!userService.add(entity))
            return Result.FAIL_LONG;

        // 处理部门岗位关联
        if (dto.getDeptPosts() != null) {
            for (int index = 0; index < dto.getDeptPosts().size(); index++) {
                String deptPost = dto.getDeptPosts().get(index);
                boolean isMain = index == 0;

                if (deptPost.startsWith("p")) {
                    Long postId = Long.parseLong(deptPost.substring(1));
                    var post = postService.getById(postId);
                    if (post.isPresent()) {
                        UserDept userDept = new UserDept();
                        userDept.setUserId(entity.getId());
                        userDept.setDeptId(post.get().getDeptId());
                        userDept.setPostId(post.get().getId());
                        userDept.setMain(isMain);
                        userDept.setStartTime(LocalDateTime.now());
                        userDeptService.add(userDept);
                    }
                } else if (deptPost.startsWith("d")) {
                    Long deptId = Long.parseLong(deptPost.substring(1));
                    var dept = deptService.getById(deptId);
                    if (dept.isPresent()) {
                        UserDept userDept = new UserDept();
                        userDept.setUserId(entity.getId());
                        userDept.setDeptId(dept.get().getId());
                        userDept.setMain(isMain);
                        userDept.setStartTime(LocalDateTime.now());
                        userDeptService.add(userDept);
                    }
                }
            }
        }

        return Result.okData(entity.getId());
    }

    @Operation(summary = "编辑用户")
    @GetMapping("/edit")
    @Perm("sys:dept:editUser")
    public Result<UserDto.EditDto> getEdit(Long id) {
        var entity = userService.getById(id, FilterOptions.DISABLE_ALL_FILTER);
        if (entity.isEmpty()) {
            return Result.fail("用户不存在");
        }

        User user = entity.get();
        List<String> deptPosts = userDeptService.getDepts(user.getId()).stream()
                .sorted((a, b) -> {
                    if (a.getMain() && !b.getMain()) return -1;
                    if (!a.getMain() && b.getMain()) return 1;
                    return Long.compare(a.getId(), b.getId());
                })
                .map(item -> item.getPostId() != null ? "p" + item.getPostId() : "d" + item.getDeptId())
                .toList();

        var result = ObjectUtil.copyTo(user, new UserDto.EditDto());
        result.setDeptPosts(deptPosts);

        return Result.okData(result);
    }

    @Operation(summary = "编辑用户")
    @PostMapping("/edit")
    @AuditLog(code = "sys:dept:editUser", value = "组织结构-编辑用户")
    @Perm("sys:dept:editUser")
    @Transactional
    public Result<Void> edit(@RequestParam Long id, @RequestBody @Valid UserDto.EditDto dto) {
        var entity = userService.getById(id, FilterOptions.DISABLE_ALL_FILTER);
        if (entity.isEmpty()) {
            return Result.fail("用户不存在");
        }

        User user = entity.get();
        user.setLoginName(dto.getLoginName());
        user.setUserName(dto.getUserName());
        user.setPhone(dto.getPhone());
        user.setEmail(dto.getEmail());
        user.setStatus(dto.getStatus());

        if (!Str.isEmpty(dto.getPassword())) {
            user.setPassword(authUserService.hashPassword(dto.getPassword()));
        }

        if (!userService.update(user))
            return Result.FAIL;

        // 获取现有的部门岗位关联
        List<UserDept> currentUserDepts = userDeptService.getDepts(id);
        List<String> deptPosts = dto.getDeptPosts() == null ? Collections.emptyList() : dto.getDeptPosts();

        // 处理新的部门岗位关联
        List<UserDept> newRelations = new ArrayList<>();

        for (int index = 0; index < deptPosts.size(); index++) {
            String deptPost = deptPosts.get(index);
            boolean isMain = index == 0;

            if (deptPost.startsWith("p")) {
                Long postId = Long.parseLong(deptPost.substring(1));

                // 查找是否已存在该岗位关联
                List<UserDept> exists = currentUserDepts.stream()
                        .filter(dept -> Objects.equals(dept.getPostId(), postId))
                        .toList();

                if (!exists.isEmpty()) {
                    // 已存在，更新main状态（如果需要）
                    for (UserDept ex : exists) {
                        if (ex.getMain() != isMain) {
                            ex.setMain(isMain);
                            CallUtil.falseTrowBizEx(userDeptService.update(ex), "更新组织关联失败");
                        }
                        // 从待删除列表中移除
                        currentUserDepts.remove(ex);
                    }
                } else {
                    // 不存在，准备创建新关联
                    var post = postService.getById(postId);
                    if (post.isPresent()) {
                        UserDept userDept = new UserDept();
                        userDept.setUserId(id);
                        userDept.setDeptId(post.get().getDeptId());
                        userDept.setPostId(post.get().getId());
                        userDept.setMain(isMain);
                        userDept.setStartTime(LocalDateTime.now());
                        newRelations.add(userDept);
                    }
                }
            } else if (deptPost.startsWith("d")) {
                Long deptId = Long.parseLong(deptPost.substring(1));

                // 查找是否已存在该部门关联（且无岗位）
                List<UserDept> exists = currentUserDepts.stream()
                        .filter(dept -> Objects.equals(dept.getDeptId(), deptId) && dept.getPostId() == null)
                        .toList();

                if (!exists.isEmpty()) {
                    // 已存在，更新main状态（如果需要）
                    for (UserDept ex : exists) {
                        if (ex.getMain() != isMain) {
                            ex.setMain(isMain);
                            CallUtil.falseTrowBizEx(userDeptService.update(ex), "更新组织关联失败");
                        }
                        // 从待删除列表中移除
                        currentUserDepts.remove(ex);
                    }
                } else {
                    // 不存在，准备创建新关联
                    var dept = deptService.getById(deptId);
                    if (dept.isPresent()) {
                        UserDept userDept = new UserDept();
                        userDept.setUserId(id);
                        userDept.setDeptId(dept.get().getId());
                        userDept.setMain(isMain);
                        userDept.setStartTime(LocalDateTime.now());
                        newRelations.add(userDept);
                    }
                }
            }
        }

        // 批量创建新关联
        if (!userDeptService.addBatch(newRelations))
            throw new BizException("创建组织关联失败");
        // 删除剩余的旧关联
        if (!userDeptService.deleteBatch(currentUserDepts))
            throw new BizException("创建组织关联失败");

        return Result.OK;
    }


    @Operation(summary = "删除用户")
    @PostMapping("/delete")
    @AuditLog(code = "sys:dept:deleteUser", value = "组织结构-删除用户")
    @Perm("sys:dept:deleteUser")
    public Result<Void> delete(@RequestBody List<Long> ids) {
        if (ids != null && !ids.isEmpty()) {
            return userService.deleteByIds(ids) ? Result.OK : Result.FAIL;
        }
        return Result.OK;
    }

    @Operation(summary = "重置密码")
    @PostMapping("/reset-password")
    @AuditLog(code = "sys:dept:userResetPassword", value = "组织结构-重置用户密码")
    @Perm("sys:dept:userResetPassword")
    public Result<String> resetPassword(@RequestParam Long id) {
        String newPassword = AuthUserService.randomPassword();
        authUserService.resetPassword(id, newPassword, FilterOptions.DISABLE_ALL_FILTER);
        return Result.okData(newPassword);
    }

    @Operation(summary = "获取用户权限")
    @GetMapping("/perm")
    @Perm("sys:dept:userPerm")
    public Result<Map<String, List<String>>> getPerms(@RequestParam Long id) {
        var userPerms = userService.getSpecialPerms(id);
        List<String> allows = userPerms.stream()
                .filter(item -> item.getType() == UserPerm.UserPermType.ALLOW)
                .map(UserPerm::getPerm)
                .toList();
        List<String> denys = userPerms.stream()
                .filter(item -> item.getType() == UserPerm.UserPermType.DENY)
                .map(UserPerm::getPerm)
                .toList();

        Map<String, List<String>> result = Map.of(
                "allows", allows,
                "denys", denys
        );

        return Result.okData(result);
    }

    @Operation(summary = "设置用户权限")
    @PostMapping("/perm")
    @AuditLog(code = "sys:dept:userPerm", value = "组织结构-设置用户权限")
    @Perm("sys:dept:userPerm")
    public Result<Void> setPerms(@RequestParam Long id, @RequestBody Map<String, List<String>> perms) {
        userService.updateSpecialPerms(id, new HashSet<>(perms.get("allows")), new HashSet<>(perms.get("denys")));
        return Result.OK;
    }
}
