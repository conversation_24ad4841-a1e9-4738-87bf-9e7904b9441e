package module.sys.admin.controller;

import infra.audit.core.AuditLog;
import infra.auth.annotation.Perm;
import infra.auth.core.AuthManager;
import infra.auth.core.Token;
import infra.auth.web.UserContext;
import infra.core.common.Result;
import infra.domain.entity.IdEntity;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import module.sys.admin.dto.AuthDto;
import module.sys.entity.Dept;
import module.sys.entity.LoginLog;
import module.sys.entity.Post;
import module.sys.entity.User;
import module.sys.service.*;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.Set;

@Tag(name = "认证管理")
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/sys/auth")
public class AuthController {

    private final AuthUserService authUserService;
    private final UserService userService;
    private final DeptService deptService;
    private final PostService postService;
    private final UserDeptService userDeptService;
    private final LoginLogService loginLogService;
    private final SliderVerifyService sliderVerifyService;
    private final AuthManager authManager;

    @Operation(summary = "初始化管理员")
    @GetMapping("/init-admin")
    public Result<Void> initAdmin() {
        String user = "admin";
        String pwd = "admin";

        if (!userService.existsUser(user, null)) {
            String hashedPwd = authUserService.hashPassword(pwd);
            userService.add(User.builder()
                    .loginName(user)
                    .userName("管理员")
                    .password(hashedPwd)
                    .admin(true)
                    .build());
        }

        return Result.OK;
    }

    @Operation(summary = "用户登录")
    @PostMapping("/login")
    public Result<Token> login(@RequestBody @Valid AuthDto.UserLoginDto dto) {
        if (!sliderVerifyService.verify(dto.getSliderData())) {
            return Result.fail("滑块行为验证失败，请重新验证");
        }

        return authUserService.login(dto.getLoginName(), dto.getPassword());
    }

    @Operation(summary = "刷新Token")
    @PostMapping("/refresh-token")
    public Result<Token> refreshToken(@RequestBody @Valid AuthDto.RefreshTokenDto dto) {
        Token token = authManager.refreshAccessToken(dto.getRefreshToken());
        if (token == null) {
            return Result.fail("当前登录已失效，请重新登录");
        }

        return Result.okData(token);
    }

    @Operation(summary = "获取当前用户信息")
    @PostMapping("/userinfo")
    @Perm
    public Result<AuthDto.UserInfoDto> userinfo() {
        var currentUser = UserContext.getCurrentUser();
        if (currentUser == null) {
            return Result.fail("未获取到用户登录信息");
        }

        var userOpt = userService.getById(currentUser.getId());
        if (userOpt.isEmpty()) {
            return Result.fail("当前用户不存在");
        }
        var user = userOpt.get();

        var depts = userDeptService.getDepts(user.getId());
        var deptPosts = depts.stream().map(item -> {
            var dept = deptService.getById(item.getDeptId());
            var post = postService.getById(item.getPostId());

            return new AuthDto.DeptPostDto(
                    dept.map(IdEntity::getId).orElse(null),
                    dept.map(Dept::getName).orElse(null),
                    post.map(IdEntity::getId).orElse(null),
                    post.map(Post::getName).orElse(null),
                    post.map(Post::getCode).orElse(null),
                    item.getMain()

            );
        }).toList();

        return Result.okData(new AuthDto.UserInfoDto(
                user.getId(),
                user.getLoginName(),
                user.getUserName(),
                deptPosts
        ));
    }

    @Operation(summary = "获取当前用户权限")
    @PostMapping("/perms")
    public Result<Set<String>> getPerms() {
        var currentUser = UserContext.getCurrentUser();
        if (currentUser == null) {
            return Result.okData(Collections.emptySet());
        }
        if (currentUser.isAdmin()) {
            return Result.okData(UserDeptService.ADMIN_PERMS);
        }

        var perms = userDeptService.getPerms(currentUser.getId(), currentUser.getDeptId());
        return Result.okData(perms);
    }

    @Operation(summary = "获取用户基本信息")
    @GetMapping("/baseinfo")
    @Perm
    public Result<AuthDto.UserBaseDto> baseInfo() {
        var currentUser = UserContext.getCurrentUser();
        if (currentUser == null) {
            return Result.fail("未获取到用户登录信息");
        }

        var userOpt = userService.getById(currentUser.getId());
        if (userOpt.isEmpty()) {
            return Result.fail("当前用户不存在");
        }
        var user = userOpt.get();

        return Result.okData(new AuthDto.UserBaseDto(
                user.getLoginName(),
                user.getUserName(),
                user.getPhone(),
                user.getEmail()));
    }

    @Operation(summary = "保存用户基本信息")
    @PostMapping("/baseinfo")
    @AuditLog(code = "auth:saveBaseInfo", value = "保存用户基本信息")
    @Perm
    public Result<Void> saveBaseInfo(@RequestBody @Valid AuthDto.UserBaseDto dto) {
        var currentUser = UserContext.getCurrentUser();
        if (currentUser == null) {
            return Result.fail("未获取到用户登录信息");
        }

        var userOpt = userService.getById(currentUser.getId());
        if (userOpt.isEmpty()) {
            return Result.fail("当前用户不存在");
        }
        var user = userOpt.get();
        user.setPhone(dto.getPhone());
        user.setEmail(dto.getEmail());
        user.setUserName(dto.getUserName());
        return userService.update(user) ? Result.OK : Result.FAIL;
    }

    @Operation(summary = "修改密码")
    @PostMapping("/change-password")
    @AuditLog(code = "auth:changePassword", value = "修改密码")
    @Perm
    public Result<Void> changePassword(@RequestBody @Valid AuthDto.ChangePasswordDto data) {
        var currentUser = UserContext.getCurrentUser();
        if (currentUser == null) {
            return Result.fail("未获取到用户登录信息");
        }

        return authUserService.changePassword(currentUser.getId(), data.getOldPassword(), data.getNewPassword());
    }

    @Operation(summary = "获取用户登录日志")
    @GetMapping("/log")
    @Perm
    public Result<List<LoginLog>> getLog() {
        var currentUser = UserContext.getCurrentUser();
        if (currentUser == null) {
            return Result.fail("未获取到用户登录信息");
        }

        var logs = loginLogService.getListByUser(currentUser.getId(), 30);
        return Result.okData(logs);
    }

    @Operation(summary = "用户登出")
    @PostMapping("/logout")
    @Perm
    public Result<Void> logout() {
        authManager.logout();
        return Result.OK;
    }
}
