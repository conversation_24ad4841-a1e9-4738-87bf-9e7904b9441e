package module.sys.admin.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

public class RuntimeDto {
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class RuntimeInfo {
        private String osInfo;
        private String pythonVersion;
        private String processor;
        private String machine;
        private Integer cpuCount;
        private Integer cpuLogicalCount;
        private Integer cpuUsage;
        private Long totalMemory;
        private Long usedMemory;
        private Long processMemory;
        private Integer processCpuUsage;
        private Integer uptime;
        private String serverTime;
        private String appDir;
        private List<DiskInfo> disks;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class DiskInfo {
        private String disk;
        private String mount;
        private String fsType;
        private Long total;
        private Long used;
        private Long free;
        private int usage;
    }
} 