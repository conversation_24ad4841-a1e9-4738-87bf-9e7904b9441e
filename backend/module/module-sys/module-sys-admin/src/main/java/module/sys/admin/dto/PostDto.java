package module.sys.admin.dto;

import infra.auth.annotation.DataPerm;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public class PostDto {
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class EditDto {
        private Long deptId;
        @NotBlank(message = "岗位名称不能为空")
        private String name;
        private String code;
        @NotNull(message = "数据权限不能为空")
        private DataPerm perm;
        private Integer sort;
        private Boolean disabled;
    }
} 