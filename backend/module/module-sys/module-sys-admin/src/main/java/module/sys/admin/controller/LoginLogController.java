package module.sys.admin.controller;

import infra.auth.annotation.Perm;
import infra.core.common.Result;
import infra.core.text.Str;
import infra.domain.query.PageParam;
import infra.domain.query.PageResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import module.sys.entity.LoginLog;
import module.sys.service.LoginLogService;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;

@Tag(name = "登录日志")
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/sys/loginlog")
public class LoginLogController {

    private final LoginLogService loginLogService;

    @Operation(summary = "查询登录日志列表")
    @GetMapping("/list")
    @Perm("sys:loginlog")
    public Result<PageResult<LoginLog>> list(String type,
                                             String key,
                                             Boolean status,
                                             LocalDate startTime,
                                             LocalDate endTime,
                                             PageParam pageParam) {
        PageResult<LoginLog> result = loginLogService.getPage(wrapper -> {
            if (!Str.isEmpty(key)) {
                String keyTrim = key.trim();
                if ("userName".equals(type)) {
                    wrapper.and(w -> w.like("user_name", keyTrim).or().like("login_name", keyTrim));
                }
                wrapper.like("ip".equals(type), "ip", keyTrim);
            }
            wrapper.eq(status != null, "status", status);
            wrapper.ge(startTime != null, "login_time", startTime);
            wrapper.le(endTime != null, "login_time", endTime);
        }, pageParam);
        return Result.okData(result);
    }
}
