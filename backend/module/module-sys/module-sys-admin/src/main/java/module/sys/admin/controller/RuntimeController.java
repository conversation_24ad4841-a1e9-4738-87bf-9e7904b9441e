package module.sys.admin.controller;

import infra.auth.annotation.Perm;
import infra.core.common.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import module.sys.admin.dto.RuntimeDto;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import oshi.SystemInfo;
import oshi.hardware.CentralProcessor;
import oshi.hardware.GlobalMemory;
import oshi.hardware.HardwareAbstractionLayer;
import oshi.software.os.FileSystem;
import oshi.software.os.OSFileStore;
import oshi.software.os.OperatingSystem;

import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

@Tag(name = "系统信息")
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/sys/runtime")
public class RuntimeController {

    @Operation(summary = "获取系统信息")
    @GetMapping("/info")
    @Perm("sys:runtime")
    public Result<RuntimeDto.RuntimeInfo> getRuntimeInfo() {
        SystemInfo systemInfo = new SystemInfo();
        HardwareAbstractionLayer hal = systemInfo.getHardware();
        OperatingSystem os = systemInfo.getOperatingSystem();

        // 获取CPU信息
        CentralProcessor processor = hal.getProcessor();

        // 获取内存信息
        GlobalMemory memory = hal.getMemory();
        MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();

        // 获取文件系统信息
        FileSystem fileSystem = os.getFileSystem();

        RuntimeDto.RuntimeInfo info = RuntimeDto.RuntimeInfo.builder()
                .osInfo(getOsInfo(os))
                .pythonVersion(System.getProperty("java.version"))
                .processor(processor.getProcessorIdentifier().getName())
                .machine(processor.getProcessorIdentifier().getMicroarchitecture())
                .cpuCount(processor.getPhysicalProcessorCount())
                .cpuLogicalCount(processor.getLogicalProcessorCount())
                .cpuUsage(getCpuUsage(processor))
                .totalMemory(memory.getTotal())
                .usedMemory(memory.getTotal() - memory.getAvailable())
                .processMemory(memoryBean.getHeapMemoryUsage().getUsed())
                .processCpuUsage(getProcessCpuUsage())
                .uptime((int) (System.currentTimeMillis() / 1000 - os.getSystemBootTime()))
                .serverTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
                .appDir(System.getProperty("user.dir"))
                .disks(getDiskInfo(fileSystem))
                .build();

        return Result.okData(info);
    }

    private String getOsInfo(OperatingSystem os) {
        return os.getFamily() + " " + os.getVersionInfo().getVersion();
    }

    private int getCpuUsage(CentralProcessor processor) {
        // 获取CPU使用率需要两次采样
        long[] prevTicks = processor.getSystemCpuLoadTicks();
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        long[] ticks = processor.getSystemCpuLoadTicks();
        double cpuLoad = processor.getSystemCpuLoadBetweenTicks(prevTicks) * 100;
        return (int) Math.round(cpuLoad);
    }

    private int getProcessCpuUsage() {
        com.sun.management.OperatingSystemMXBean osBean =
                (com.sun.management.OperatingSystemMXBean) ManagementFactory.getOperatingSystemMXBean();
        return (int) Math.round(osBean.getProcessCpuLoad() * 100);
    }

    private List<RuntimeDto.DiskInfo> getDiskInfo(FileSystem fileSystem) {
        List<RuntimeDto.DiskInfo> disks = new ArrayList<>();

        for (OSFileStore store : fileSystem.getFileStores()) {
            // 过滤虚拟文件系统，只保留物理磁盘
            if (isPhysicalFileStore(store)) {
                long total = store.getTotalSpace();
                long free = store.getFreeSpace();
                long used = total - free;
                int usage = total > 0 ? (int) Math.round((double) used / total * 100) : 0;

                RuntimeDto.DiskInfo diskInfo = RuntimeDto.DiskInfo.builder()
                        .disk(store.getName())
                        .mount(store.getMount())
                        .fsType(store.getType())
                        .total(total)
                        .used(used)
                        .free(free)
                        .usage(usage)
                        .build();

                disks.add(diskInfo);
            }
        }

        return disks;
    }

    private boolean isPhysicalFileStore(OSFileStore store) {
        String type = store.getType().toLowerCase();
        String mount = store.getMount();
        String name = store.getName().toLowerCase();

        // 直接排除ZFS文件系统
        if (type.contains("zfs")) {
            return false;
        }

        // 排除虚拟文件系统类型
        if (type.matches("tmpfs|devtmpfs|squashfs|overlay|proc|sysfs|cgroup.*|debugfs|securityfs|fuse.*|aufs|unionfs")) {
            return false;
        }

        // 排除名称中包含虚拟标识的
        if (name.matches(".*tmpfs.*|.*devpts.*|.*proc.*|.*sysfs.*|.*cgroup.*|.*mqueue.*|.*hugetlbfs.*|.*configfs.*|.*fusectl.*")) {
            return false;
        }

        // 排除系统挂载点
        if (mount.matches("/proc.*|/sys.*|/dev.*|/run.*|/snap/.*|/boot/efi|/var/lib/docker/.*|/var/lib/containers/.*")) {
            return false;
        }

        // Windows下排除网络驱动器和CD驱动器
        if (System.getProperty("os.name").toLowerCase().contains("windows")) {
            if (type.equals("unknown") || type.equals("cdfs") || type.equals("udf")) {
                return false;
            }
        }

        // 最基本的验证：必须有有效的存储空间
        return store.getTotalSpace() > 0;
    }

}
