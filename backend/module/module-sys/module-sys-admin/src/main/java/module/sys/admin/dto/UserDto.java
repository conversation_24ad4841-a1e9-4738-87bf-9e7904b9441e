package module.sys.admin.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import module.sys.entity.User;

import java.util.List;

public class UserDto {
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class EditDto {
        @NotBlank(message = "登录账号不能为空")
        private String loginName;
        @NotBlank(message = "用户姓名不能为空")
        private String userName;
        private String password;
        private String phone;
        private String email;
        @NotNull(message = "用户状态不能为空")
        private User.UserStatus status;
        private List<String> deptPosts;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class InfoDto {
        private Long id;
        private String loginName;
        private String userName;
        private String phone;
        private String email;
        private User.UserStatus status;
        private List<String> deptPosts;
    }
}
