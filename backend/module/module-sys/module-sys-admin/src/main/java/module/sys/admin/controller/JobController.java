package module.sys.admin.controller;

import infra.audit.core.AuditLog;
import infra.auth.annotation.Perm;
import infra.core.common.Result;
import infra.domain.query.PageParam;
import infra.domain.query.PageResult;
import infra.task.core.TaskScheduler;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import module.sys.entity.Job;
import module.sys.entity.JobLog;
import module.sys.service.JobService;
import module.sys.service.JobLogService;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Tag(name = "后台任务")
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/sys/job")
public class JobController {

    private final JobService jobService;
    private final JobLogService jobLogService;
    private final TaskScheduler taskScheduler;

    @Operation(summary = "当前任务列表")
    @GetMapping("/list")
    @Perm("sys:job")
    public Result<List<Job>> list() {
        List<Job> jobs = jobService.getList();
        return Result.okData(jobs);
    }

    @Operation(summary = "执行任务")
    @PostMapping("/run")
    @AuditLog(code = "sys:job:run", value = "后台任务-执行任务")
    @Perm("sys:job:run")
    public Result<Void> runJob(@RequestBody List<Long> ids) {
        List<Job> jobs = jobService.getListByIds(ids);
        for (Job job : jobs) {
            if (job != null && job.getStatus() != Job.JobStatus.RUNNING) {
                job.setRunCount(job.getRunCount() + 1);
                job.setRunLastTime(LocalDateTime.now());
                jobService.update(job);
                taskScheduler.executeJobNow(job.getJobId());
            }
        }

        return Result.OK;
    }

    @Operation(summary = "启用任务")
    @PostMapping("/enable")
    @AuditLog(code = "sys:job:enable", value = "后台任务-启用任务")
    @Perm("sys:job:enable")
    public Result<Void> enableJob(@RequestBody List<Long> ids) {
        return jobService.updateBatch(wrapper ->
                wrapper.in("id", ids)
                        .eq("status", Job.JobStatus.STOP)
                        .set("status", Job.JobStatus.WAITING))
                ? Result.OK : Result.FAIL;
    }

    @Operation(summary = "禁用任务")
    @PostMapping("/disable")
    @AuditLog(code = "sys:job:disable", value = "后台任务-禁用任务")
    @Perm("sys:job:disable")
    public Result<Void> disableJob(@RequestBody List<Long> ids) {
        return jobService.updateBatch(wrapper ->
                wrapper.in("id", ids)
                        .set("status", Job.JobStatus.STOP))
                ? Result.OK : Result.FAIL;
    }

    @Operation(summary = "查询任务日志列表")
    @GetMapping("/log")
    @Perm("sys:job:log")
    public Result<PageResult<JobLog>> jobLogs(
            String jobId,
            Boolean status,
            LocalDate startTime,
            LocalDate endTime,
            PageParam pageParam) {

        var result = jobLogService.getPage(wrapper -> {
            wrapper.like(jobId != null, "job_id", jobId);
            wrapper.eq(status != null, "success", status);
            wrapper.ge(startTime != null, "start_time", startTime);
            wrapper.le(endTime != null, "start_time", endTime);
        }, pageParam);

        return Result.okData(result);
    }
}
