package module.sys.admin.controller;

import infra.audit.core.AuditLog;
import infra.auth.annotation.Perm;
import infra.core.common.ObjectUtil;
import infra.core.common.Result;
import infra.domain.model.TreeNode;

import infra.domain.service.FilterOptions;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import module.sys.admin.dto.DeptDto;
import module.sys.entity.Dept;
import module.sys.service.DeptService;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "组织结构-部门")
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/sys/dept")
public class DeptController {

    private final DeptService deptService;

    @Operation(summary = "组织结构树")
    @GetMapping("/tree")
    @Perm("sys:dept")
    public Result<List<TreeNode>> tree() {
        List<TreeNode> tree = deptService.getTreeWithPost(null, true, FilterOptions.DISABLE_ALL_FILTER);
        return Result.okData(tree);
    }

    @Operation(summary = "新增部门")
    @PostMapping("/add")
    @AuditLog(code = "sys:dept:addDept", value = "组织结构-新增部门")
    @Perm("sys:dept:addDept")
    public Result<Long> add(@RequestBody @Valid DeptDto.EditDto dto) {
        Dept entity = Dept.builder()
                .name(dto.getName())
                .type(dto.getType())
                .leaderId(dto.getLeaderId())
                .leaderName(dto.getLeaderName())
                .contact(dto.getContact())
                .disabled(dto.getDisabled())
                .build();
        entity.setSort(dto.getSort());
        entity.setParentId(dto.getParentId());

        return deptService.add(entity) ?
                Result.okData(entity.getId()) :
                Result.FAIL_LONG;
    }

    @Operation(summary = "编辑部门")
    @GetMapping("/edit")
    @Perm("sys:dept:editDept")
    public Result<DeptDto.EditDto> getEdit(Long id) {
        var deptOpt = deptService.getById(id, FilterOptions.DISABLE_ALL_FILTER);
        return Result.okData(
                ObjectUtil.copyTo(deptOpt.orElse(null), new DeptDto.EditDto())
        );
    }

    @Operation(summary = "编辑部门")
    @PostMapping("/edit")
    @AuditLog(code = "sys:dept:editDept", value = "组织结构-编辑部门")
    @Perm("sys:dept:editDept")
    public Result<Void> edit(@RequestParam Long id, @RequestBody @Valid DeptDto.EditDto dto) {
        var entity = deptService.getById(id, FilterOptions.DISABLE_ALL_FILTER);
        if (entity.isEmpty()) {
            return Result.fail("部门不存在");
        }

        Dept dept = entity.get();
        dept.setParentId(dto.getParentId());
        dept.setName(dto.getName());
        dept.setType(dto.getType());
        dept.setLeaderId(dto.getLeaderId());
        dept.setLeaderName(dto.getLeaderName());
        dept.setContact(dto.getContact());
        dept.setDisabled(dto.getDisabled());
        dept.setSort(dto.getSort());

        return deptService.update(dept) ? Result.OK : Result.FAIL;
    }

    @Operation(summary = "删除部门")
    @PostMapping("/delete")
    @AuditLog(code = "sys:dept:deleteDept", value = "组织结构-删除部门")
    @Perm("sys:dept:deleteDept")
    public Result<Void> delete(@RequestBody List<Long> ids) {
        if (ids != null && !ids.isEmpty()) {
            for (Long id : ids) {
                return deptService.deleteById(id) ? Result.OK : Result.FAIL;
            }
        }
        return Result.OK;
    }
}
