package module.sys.admin.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import infra.auth.annotation.Perm;
import infra.core.common.Result;
import infra.core.text.Str;
import infra.core.text.DateFormat;
import infra.domain.query.PageParam;
import infra.domain.query.PageResult;
import infra.report.excel.exporter.ExcelExporter;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import module.sys.entity.AuditLog;
import module.sys.service.AuditLogService;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.time.LocalDate;

@Tag(name = "审计日志")
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/sys/auditlog")
public class AuditLogController {

    private final AuditLogService auditLogService;

    @Operation(summary = "查询审计日志列表")
    @GetMapping("/list")
    @Perm("sys:auditlog")
    public Result<PageResult<AuditLog>> list(@Valid QueryDto query, PageParam pageParam) {
        PageResult<AuditLog> result = auditLogService.getPage(wrapper -> applyQueryCondition(wrapper, query), pageParam);
        return Result.okData(result);
    }

    @Operation(summary = "导出审计日志")
    @PostMapping("/export")
    @infra.audit.core.AuditLog(code = "sys:auditlog:export", value = "审计日志-导出")
    @Perm("sys:auditlog:export")
    public void exportAuditLogs(@RequestBody @Valid QueryDto query, HttpServletResponse response) throws IOException {
        ExcelExporter.<AuditLog>create()
                .sheet("审计日志")
                .columns(cols -> cols
                        .add("ID", AuditLog::getId)
                        .add("登录账户", item -> Str.nullToEmpty(item.getLoginName()))
                        .add("用户姓名", item -> Str.nullToEmpty(item.getUserName()))
                        .add("模块", item -> Str.nullToEmpty(item.getModule()))
                        .add("操作", item -> Str.nullToEmpty(item.getDetail()))
                        .add("操作时间", item -> item.getStartTime() != null ?
                                item.getStartTime().format(DateFormat.DATE_TIME_FORMATTER) : "")
                        .add("操作IP", item -> Str.nullToEmpty(item.getIp()))
                        .add("执行结果", item -> item.getSuccess() ? "成功" : "失败")
                        .add("耗时(ms)", item -> item.getUseTime() + "ms")
                        .add("错误消息", item -> Str.nullToEmpty(item.getError()))
                        .add("请求URL", item -> Str.nullToEmpty(item.getUrl())))
                .fromService(auditLogService, wrapper -> applyQueryCondition(wrapper, query))
                .export(response, "审计日志");
    }

    /**
     * 条件处理
     */
    private QueryWrapper<AuditLog> applyQueryCondition(QueryWrapper<AuditLog> wrapper, QueryDto query) {
        if (!Str.isEmpty(query.key())) {
            String type = query.type();
            String key = query.key().trim();
            if ("userName".equals(type)) {
                wrapper.and(w -> w.like("user_name", key).or().like("login_name", key));
            }
            wrapper.like("detail".equals(type), "detail", key);
            wrapper.like("ip".equals(type), "ip", key);
            wrapper.like("url".equals(type), "url", key);
            wrapper.eq("module".equals(type), "module", key);
        }
        wrapper.eq(query.status() != null, "success", query.status());
        wrapper.ge(query.startTime() != null, "start_time", query.startTime());
        wrapper.le(query.endTime() != null, "start_time", query.endTime());
        wrapper.ge(query.useTime() != null, "use_time", query.useTime());
        return wrapper;
    }

    public record QueryDto(
            String type,
            String key,
            Boolean status,
            LocalDate startTime,
            LocalDate endTime,
            Integer useTime
    ) {

    }
}
