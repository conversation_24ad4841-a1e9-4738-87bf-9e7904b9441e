<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>basic</groupId>
        <artifactId>basic</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>basic-configurer</artifactId>
    <description>提供常用的，统一的配置</description>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-autoconfigure</artifactId>
        </dependency>

        <!-- Spring/Web/Reids -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webmvc</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>jakarta.validation</groupId>
            <artifactId>jakarta.validation-api</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>jakarta.servlet</groupId>
            <artifactId>jakarta.servlet-api</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-tx</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-redis</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson-spring-boot-starter</artifactId>
            <scope>provided</scope>
        </dependency>

        <!-- 其它依赖 -->
        <dependency>
            <groupId>infra</groupId>
            <artifactId>infra-core</artifactId>
            <version>${revision}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>infra</groupId>
            <artifactId>infra-auth</artifactId>
            <version>${revision}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>infra</groupId>
            <artifactId>infra-oss</artifactId>
            <version>${revision}</version>
            <scope>provided</scope>
        </dependency>
    </dependencies>
</project>