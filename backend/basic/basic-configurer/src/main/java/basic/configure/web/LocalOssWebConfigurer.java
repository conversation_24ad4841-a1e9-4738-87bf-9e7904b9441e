package basic.configure.web;

import infra.oss.config.OssConfigProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.io.File;
import java.nio.file.Path;

/**
 * 本地OSS存储的Web静态资源配置
 */
@Slf4j
@RequiredArgsConstructor
public class LocalOssWebConfigurer implements WebMvcConfigurer {

    private final OssConfigProperties ossConfig;

    @Override
    public void addResourceHandlers(@NotNull ResourceHandlerRegistry registry) {
        var config = ossConfig.getLocal();
        if (config == null) {
            return;
        }

        String uploadPath = config.getPath();
        String baseUrl = config.getBaseUrl();

        if (uploadPath == null || baseUrl == null) {
            log.warn("[配置] 本地OSS路径或URL配置为空，跳过静态资源映射");
            return;
        }

        // 解析上传路径为绝对路径
        Path absolutePath = Path.of(uploadPath).toAbsolutePath().normalize();
        String resourceLocation = "file:" + absolutePath + File.separator;

        // 从base_url中提取路径映射模式
        String urlPattern = extractUrlPattern(baseUrl);

        log.info("[配置] 本地OSS静态资源映射: {} -> {}", urlPattern, resourceLocation);

        registry.addResourceHandler(urlPattern)
                .addResourceLocations(resourceLocation)
                .setCachePeriod(3600)
                .resourceChain(true);
    }

    /**
     * 从base_url中提取URL路径模式
     */
    private String extractUrlPattern(String baseUrl) {
        try {
            // 移除协议和主机部分，只保留路径
            String path = baseUrl.replaceFirst("^https?://[^/]+", "");

            // 确保以/开头
            if (!path.startsWith("/")) {
                path = "/" + path;
            }

            // 确保以/**结尾用于匹配子路径
            if (!path.endsWith("/**")) {
                if (path.endsWith("/")) {
                    path = path + "**";
                } else {
                    path = path + "/**";
                }
            }

            return path;
        } catch (Exception e) {
            log.warn("[配置] 解析base_url失败: {}, 使用默认模式", baseUrl, e);
            return "/upload/**";
        }
    }
}
