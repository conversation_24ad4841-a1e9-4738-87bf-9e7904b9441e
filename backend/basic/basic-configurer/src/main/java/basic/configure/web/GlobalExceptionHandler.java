package basic.configure.web;

import infra.auth.exception.AuthException;
import infra.auth.exception.PermException;
import infra.core.common.Result;
import infra.core.exception.BizException;
import jakarta.validation.ConstraintViolationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataAccessException;
import org.springframework.http.HttpStatus;
import org.springframework.validation.BindException;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.servlet.NoHandlerFoundException;
import org.springframework.web.servlet.resource.NoResourceFoundException;

/**
 * 全局异常处理
 */
@ResponseBody
@ControllerAdvice
@Slf4j
public class GlobalExceptionHandler {
    /**
     * 参数校验异常处理
     */
    @ExceptionHandler(BindException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result<String> handleBindException(BindException ex) {
        var fieldError = ex.getBindingResult().getFieldError();
        if (fieldError != null) {
            return Result.fail(fieldError.getDefaultMessage(), fieldError.getField());
        } else {
            return Result.fail(ex.getMessage());
        }
    }

    /**
     * 参数校验异常处理
     * 在业务层抛出异常时，会抛出ConstraintViolationException
     */
    @ExceptionHandler(ConstraintViolationException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result<String> handleConstraintViolationException(ConstraintViolationException ex) {
        var first = ex.getConstraintViolations().stream().findFirst();
        return first.<Result<String>>map(constraintViolation -> Result.fail(constraintViolation.getMessage())).orElseGet(() -> Result.fail(ex.getMessage()));
    }

    /**
     * 认证异常处理
     */
    @ExceptionHandler(AuthException.class)
    @ResponseStatus(HttpStatus.UNAUTHORIZED)
    public Result<String> handleAuthException(AuthException ex) {
        return Result.fail(401, ex.getMessage());
    }

    /**
     * 权限异常处理
     */
    @ExceptionHandler(PermException.class)
    @ResponseStatus(HttpStatus.FORBIDDEN)
    public Result<String> handlePermException(PermException ex) {
        return Result.fail(403, ex.getMessage());
    }

    /**
     * 业务异常处理
     */
    @ExceptionHandler(BizException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result<Void> handleBizException(BizException ex) {
        return Result.fail(ex.getCode(), ex.getMessage());
    }

    /**
     * 404异常处理
     */
    @ExceptionHandler(NoHandlerFoundException.class)
    @ResponseStatus(HttpStatus.NOT_FOUND)
    public Result<Void> handleNoHandlerFoundException(NoHandlerFoundException ex) {
        return Result.fail(404, "请求的资源不存在");
    }

    /**
     * 404异常处理
     */
    @ExceptionHandler(NoResourceFoundException.class)
    @ResponseStatus(HttpStatus.NOT_FOUND)
    public Result<Void> handleNoResourceFoundException(NoResourceFoundException ex) {
        return Result.fail(404, "请求的资源不存在");
    }

    /**
     * 请求方法不支持异常处理
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    @ResponseStatus(HttpStatus.METHOD_NOT_ALLOWED)
    public Result<Void> handleHttpRequestMethodNotSupportedException(HttpRequestMethodNotSupportedException ex) {
        return Result.fail(405, "不支持的请求方法: " + ex.getMethod());
    }

    /**
     * 媒体类型不支持异常处理
     */
    @ExceptionHandler(HttpMediaTypeNotSupportedException.class)
    @ResponseStatus(HttpStatus.UNSUPPORTED_MEDIA_TYPE)
    public Result<Void> handleHttpMediaTypeNotSupportedException(HttpMediaTypeNotSupportedException ex) {
        return Result.fail(415, "不支持的媒体类型");
    }

    /**
     * 数据访问异常处理，不应该向客户端抛message
     */
    @ExceptionHandler(DataAccessException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public Result<Void> handleDataAccessException(Exception ex) {
        log.warn("数据处理异常：{}", ex.getMessage(), ex);
        return Result.fail("服务器处理数据时发生错误");
    }

    /**
     * 其它全局异常处理
     */
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public Result<Void> handleException(Exception ex) {
        log.warn("请求发生异常：{}", ex.getMessage(), ex);
        return Result.fail(ex.getMessage());
    }
}
