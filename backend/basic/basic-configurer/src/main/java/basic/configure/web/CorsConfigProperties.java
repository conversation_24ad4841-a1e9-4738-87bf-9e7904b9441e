package basic.configure.web;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

/**
 * CORS跨域配置
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Validated
@ConfigurationProperties(prefix = "app.cors")
public class CorsConfigProperties {
    // 是否启用CORS
    private boolean enabled = false;
    // 允许的源
    private String[] allowedOrigins = {"*"};
    // 允许的请求方法
    private String[] allowedMethods = {"GET", "POST", "PUT", "DELETE", "OPTIONS"};
    // 允许的请求头
    private String[] allowedHeaders = {"*"};
    // 是否允许携带Cookie
    private boolean allowCredentials = true;
    // 允许访问的Header，默认为Content-Disposition
    private String exposedHeaders = "Content-Disposition";
    // 缓存时间
    private long maxAge = 3600;
}
