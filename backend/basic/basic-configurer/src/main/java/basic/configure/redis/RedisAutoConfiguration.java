package basic.configure.redis;

import lombok.extern.slf4j.Slf4j;
import org.redisson.codec.JsonJacksonCodec;
import org.redisson.spring.starter.RedissonAutoConfigurationCustomizer;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

/**
 * Redis统一配置
 */
@AutoConfiguration
@Slf4j
public class RedisAutoConfiguration {
    /**
     * SpringRedis配置 - 使用JSON序列化
     */
    @Bean
    @ConditionalOnMissingBean
    public RedisTemplate<String, Object> basicRedisTemplate(RedisConnectionFactory factory) {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(factory);

        // 使用 GenericJackson2JsonRedisSerializer 进行 JSON 序列化
        GenericJackson2JsonRedisSerializer jsonSerializer = new GenericJackson2JsonRedisSerializer();
        StringRedisSerializer stringSerializer = new StringRedisSerializer();

        // Key 使用字符串序列化
        template.setKeySerializer(stringSerializer);
        template.setHashKeySerializer(stringSerializer);

        // Value 使用 JSON 序列化
        template.setValueSerializer(jsonSerializer);
        template.setHashValueSerializer(jsonSerializer);

        template.setDefaultSerializer(jsonSerializer);
        template.afterPropertiesSet();

        log.info("[配置] Spring RedisTemplate 已创建，使用 JSON 序列化");
        return template;
    }

    /**
     * 字符串RedisTemplate - 纯字符串操作
     */
    @Bean
    @ConditionalOnMissingBean
    public StringRedisTemplate basicStringRedisTemplate(RedisConnectionFactory factory) {
        StringRedisTemplate template = new StringRedisTemplate(factory);
        log.info("[配置] StringRedisTemplate 已创建");
        return template;
    }

    /**
     * Redisson配置 - 使用JSON序列化
     */
    @Bean
    @ConditionalOnMissingBean
    public RedissonAutoConfigurationCustomizer basicRedissonCustomizer() {
        return configuration -> {
            configuration.setCodec(new JsonJacksonCodec());
            log.info("[配置] Redisson 序列化器已配置为 JsonJacksonCodec，与 Spring Redis 保持一致");
        };
    }

}
