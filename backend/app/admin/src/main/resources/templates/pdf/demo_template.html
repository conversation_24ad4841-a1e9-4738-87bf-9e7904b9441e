<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8"/>
    <title>测试报告</title>
    <link rel="stylesheet" type="text/css" href="/css/report.css"/>
    <style>
        @page {
            size: A4;
            margin: 2cm;

            @top-center {
                content: element(header);
            }

            @bottom-right {
                content: element(footer);
            }
        }

        #header {
            position: running(header);
            text-align: center;
        }

        #footer {
            position: running(footer);
            text-align: right;
            font-size: 10px;
            color: #999;
        }

        #footer .page-number:after {
            content: "第 " counter(page) " 页 / 共 " counter(pages) " 页";
        }

        body {
            font-family: 'SimHei', sans-serif;
        }

        .content-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .content-table th, .content-table td {
            border: 1px solid #ccc;
            padding: 8px;
            text-align: left;
        }

        .content-table th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>

<!-- 页眉 -->
<div id="header">
    <img src="/images/logo.png" style="height: 40px;"/>
    <h2>演示报告</h2>
</div>

<!-- 页脚 -->
<div id="footer">
    <span class="page-number"></span>
</div>

<!-- 主体内容 -->
<main>
    <h1>你好, <span th:text="${userName}">用户</span>!</h1>
    <p>这是一个通过 infra-report 模块生成的复杂 PDF 报告。</p>

    <h2>报告详情</h2>
    <p>这份报告创建于 <span th:text="${reportDate}"></span>，旨在演示框架的各项功能。</p>

    <table class="content-table">
        <thead>
        <tr>
            <th>项目</th>
            <th>值</th>
        </tr>
        </thead>
        <tbody>
        <tr th:each="item : ${details}">
            <td th:text="${item.key}"></td>
            <td th:text="${item.value}"></td>
        </tr>
        </tbody>
    </table>

</main>

</body>
</html>
