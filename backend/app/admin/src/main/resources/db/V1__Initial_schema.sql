CREATE TABLE "public"."sys_dept" ( 
  "id" BIGSERI<PERSON>,
  "parent_id" BIGINT NULL,
  "path" VARCHAR(255) NULL,
  "sort" INTEGER NOT NULL DEFAULT 0 ,
  "name" VARCHAR(63) NOT NULL,
  "type" SMALLINT NOT NULL DEFAULT 0 ,
  "leader_id" BIGINT NULL,
  "leader_name" VARCHAR(15) NULL,
  "contact" VARCHAR(63) NULL,
  "disabled" BOOLEAN NOT NULL DEFAULT false ,
  "create_time"TIMESTAMP(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ,
  "create_by" BIGINT NULL,
  "create_by_name" VARCHAR(15) NULL,
  "update_time"TIMESTAMP(0) NULL DEFAULT CURRENT_TIMESTAMP ,
  "update_by" BIGINT NULL,
  "update_by_name" VARCHAR(15) NULL,
  CONSTRAINT "sys_dept_pkey" PRIMARY KEY ("id")
);
CREATE TABLE "public"."sys_job_log" ( 
  "id" BIGSERIAL,
  "job_id" VARCHAR(15) NOT NULL,
  "start_time"TIMESTAMP(0) NOT NULL,
  "end_time"TIMESTAMP(0) NULL,
  "use_time" BIGINT NOT NULL,
  "success" BOOLEAN NOT NULL,
  "msg" VARCHAR(2047) NULL,
  CONSTRAINT "sys_job_log_pkey" PRIMARY KEY ("id")
);
CREATE TABLE "public"."sys_valid_code" ( 
  "id" BIGSERIAL,
  "code" VARCHAR(6) NOT NULL,
  "extra" VARCHAR(31) NULL,
  "expiry_time"TIMESTAMP(0) NOT NULL,
  "used" BOOLEAN NOT NULL DEFAULT false ,
  "try_count" INTEGER NOT NULL DEFAULT 0 ,
  CONSTRAINT "sys_valid_code_pkey" PRIMARY KEY ("id")
);
CREATE TABLE "public"."sys_job" ( 
  "id" BIGSERIAL,
  "job_id" VARCHAR(15) NOT NULL,
  "job_desc" VARCHAR(63) NULL,
  "cron" VARCHAR(63) NOT NULL,
  "cron_desc" VARCHAR(63) NULL,
  "sort" INTEGER NOT NULL DEFAULT 0 ,
  "status" SMALLINT NOT NULL DEFAULT 0 ,
  "run_last_time"TIMESTAMP(0) NULL,
  "next_run_time"TIMESTAMP(0) NULL,
  "run_count" BIGINT NOT NULL DEFAULT 0 ,
  "run_success" BIGINT NOT NULL DEFAULT 0 ,
  "run_fail" BIGINT NOT NULL DEFAULT 0 ,
  "version" BIGINT NOT NULL DEFAULT 0 ,
  CONSTRAINT "sys_job_pkey" PRIMARY KEY ("id"),
  CONSTRAINT "sys_job_job_id_key" UNIQUE ("job_id")
);
CREATE TABLE "public"."sys_post_perm" ( 
  "id" BIGSERIAL,
  "post_id" BIGINT NOT NULL,
  "perm" VARCHAR(31) NOT NULL,
  CONSTRAINT "sys_post_perm_pkey" PRIMARY KEY ("id")
);
CREATE TABLE "public"."sys_user_perm" ( 
  "id" BIGSERIAL,
  "user_id" BIGINT NOT NULL,
  "type" SMALLINT NOT NULL DEFAULT 0 ,
  "perm" VARCHAR(31) NOT NULL,
  "create_time"TIMESTAMP(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ,
  "create_by" BIGINT NULL,
  "create_by_name" VARCHAR(15) NULL,
  "update_time"TIMESTAMP(0) NULL DEFAULT CURRENT_TIMESTAMP ,
  "update_by" BIGINT NULL,
  "update_by_name" VARCHAR(15) NULL,
  CONSTRAINT "sys_user_perm_pkey" PRIMARY KEY ("id")
);
CREATE TABLE "public"."sys_user_dept" ( 
  "id" BIGSERIAL,
  "user_id" BIGINT NOT NULL,
  "dept_id" BIGINT NOT NULL,
  "post_id" BIGINT NULL,
  "main" BOOLEAN NOT NULL DEFAULT true ,
  "start_time"TIMESTAMP(0) NOT NULL,
  "end_time"TIMESTAMP(0) NULL,
  "create_time"TIMESTAMP(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ,
  "create_by" BIGINT NULL,
  "create_by_name" VARCHAR(15) NULL,
  "update_time"TIMESTAMP(0) NULL DEFAULT CURRENT_TIMESTAMP ,
  "update_by" BIGINT NULL,
  "update_by_name" VARCHAR(15) NULL,
  CONSTRAINT "sys_user_dept_pkey" PRIMARY KEY ("id")
);
CREATE TABLE "public"."sys_user" ( 
  "id" BIGSERIAL,
  "user_name" VARCHAR(15) NOT NULL,
  "login_name" VARCHAR(15) NOT NULL,
  "pinyin" VARCHAR(63) NOT NULL,
  "py" VARCHAR(15) NOT NULL,
  "password" VARCHAR(96) NULL,
  "phone" VARCHAR(15) NULL,
  "email" VARCHAR(63) NULL,
  "admin" BOOLEAN NOT NULL DEFAULT false ,
  "status" SMALLINT NOT NULL DEFAULT 0 ,
  "last_login"TIMESTAMP(0) NULL,
  "create_time"TIMESTAMP(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ,
  "create_by" BIGINT NULL,
  "create_by_name" VARCHAR(15) NULL,
  "update_time"TIMESTAMP(0) NULL DEFAULT CURRENT_TIMESTAMP ,
  "update_by" BIGINT NULL,
  "update_by_name" VARCHAR(15) NULL,
  CONSTRAINT "sys_user_pkey" PRIMARY KEY ("id"),
  CONSTRAINT "sys_user_login_name_key" UNIQUE ("login_name")
);
CREATE TABLE "public"."sys_data_dict" ( 
  "id" BIGSERIAL,
  "name" VARCHAR(15) NOT NULL,
  "code" VARCHAR(15) NOT NULL,
  "info" VARCHAR(63) NULL,
  "sort" INTEGER NOT NULL DEFAULT 0 ,
  "create_time"TIMESTAMP(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ,
  "create_by" BIGINT NULL,
  "create_by_name" VARCHAR(15) NULL,
  "update_time"TIMESTAMP(0) NULL DEFAULT CURRENT_TIMESTAMP ,
  "update_by" BIGINT NULL,
  "update_by_name" VARCHAR(15) NULL,
  CONSTRAINT "sys_data_dict_pkey" PRIMARY KEY ("id")
);
CREATE TABLE "public"."sys_data_dict_value" ( 
  "id" BIGSERIAL,
  "dict_id" BIGINT NOT NULL,
  "name" VARCHAR(15) NOT NULL,
  "code" VARCHAR(15) NOT NULL,
  "sort" INTEGER NOT NULL DEFAULT 0 ,
  "disabled" BOOLEAN NOT NULL DEFAULT false ,
  "create_time"TIMESTAMP(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ,
  "create_by" BIGINT NULL,
  "create_by_name" VARCHAR(15) NULL,
  "update_time"TIMESTAMP(0) NULL DEFAULT CURRENT_TIMESTAMP ,
  "update_by" BIGINT NULL,
  "update_by_name" VARCHAR(15) NULL,
  CONSTRAINT "sys_data_dict_value_pkey" PRIMARY KEY ("id")
);
CREATE TABLE "public"."sys_upfile" ( 
  "id" BIGSERIAL,
  "file_name" VARCHAR(255) NOT NULL,
  "object_key" VARCHAR(255) NOT NULL,
  "full_path" VARCHAR(2047) NOT NULL,
  "file_ext" VARCHAR(15) NULL,
  "file_size" BIGINT NOT NULL,
  "info" VARCHAR(255) NULL,
  "create_time"TIMESTAMP(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ,
  "create_by" BIGINT NULL,
  "create_by_name" VARCHAR(15) NULL,
  "update_time"TIMESTAMP(0) NULL DEFAULT CURRENT_TIMESTAMP ,
  "update_by" BIGINT NULL,
  "update_by_name" VARCHAR(15) NULL,
  "deleted" BOOLEAN NOT NULL DEFAULT false ,
  CONSTRAINT "sys_upfile_pkey" PRIMARY KEY ("id")
);
CREATE TABLE "public"."sys_post" ( 
  "id" BIGSERIAL,
  "dept_id" BIGINT NOT NULL,
  "code" VARCHAR(15) NOT NULL,
  "name" VARCHAR(15) NOT NULL,
  "perm" SMALLINT NOT NULL DEFAULT 0 ,
  "sort" INTEGER NOT NULL DEFAULT 0 ,
  "disabled" BOOLEAN NOT NULL DEFAULT false ,
  "create_time"TIMESTAMP(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ,
  "create_by" BIGINT NULL,
  "create_by_name" VARCHAR(15) NULL,
  "update_by" BIGINT NULL,
  "update_by_name" VARCHAR(15) NULL,
  "update_time"TIMESTAMP(0) NULL DEFAULT CURRENT_TIMESTAMP ,
  CONSTRAINT "sys_post_pkey" PRIMARY KEY ("id")
);
CREATE TABLE "public"."sys_setting" ( 
  "id" BIGSERIAL,
  "name" VARCHAR(15) NOT NULL,
  "code" VARCHAR(15) NOT NULL,
  "value" TEXT NULL,
  CONSTRAINT "sys_setting_pkey" PRIMARY KEY ("id"),
  CONSTRAINT "sys_setting_code_key" UNIQUE ("code")
);
CREATE TABLE "public"."sys_login_log" ( 
  "id" BIGSERIAL,
  "user_id" BIGINT NOT NULL,
  "login_name" VARCHAR(15) NULL,
  "user_name" VARCHAR(15) NULL,
  "login_time"TIMESTAMP(0) NOT NULL,
  "login_type" SMALLINT NOT NULL,
  "ip" VARCHAR(15) NULL,
  "client" VARCHAR(511) NULL,
  "success" BOOLEAN NOT NULL,
  "error" VARCHAR(511) NULL,
  CONSTRAINT "sys_login_log_pkey" PRIMARY KEY ("id")
);
CREATE TABLE "public"."sys_audit_log" ( 
  "id" BIGSERIAL,
  "module" VARCHAR(15) NULL,
  "code" VARCHAR(63) NOT NULL,
  "detail" VARCHAR(127) NOT NULL,
  "user_id" BIGINT NULL,
  "login_name" VARCHAR(15) NULL,
  "user_name" VARCHAR(15) NULL,
  "url" VARCHAR(2047) NOT NULL,
  "ip" VARCHAR(15) NOT NULL,
  "data" TEXT NULL,
  "success" BOOLEAN NOT NULL,
  "error" VARCHAR(2047) NULL,
  "start_time"TIMESTAMP(0) NOT NULL,
  "use_time" BIGINT NOT NULL,
  CONSTRAINT "sys_audit_log_pkey" PRIMARY KEY ("id")
);
