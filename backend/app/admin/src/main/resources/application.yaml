server:
  compression:
    enabled: true

spring:
  application:
    name: admin
  profiles:
    active: dev
  threads:
    virtual:
      enabled: true
  datasource:
    driver-class-name: org.postgresql.Driver
    url: *************************************
    username: postgres
    # password: ${DB_PASSWORD}
    password: ruson523
    hikari:
      pool-name: HikariCP
      minimum-idle: 4
      maximum-pool-size: 32
      idle-timeout: 30000
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: SELECT 1
  data:
    redis:
        host: localhost
        port: 6379
        password: ruson523
        # password: ${REDIS_PASSWORD}
        lettuce:
          pool:
            max-active: 16
            max-idle: 8
            min-idle: 0
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 1024MB
  main:
    banner-mode: off
  flyway:
    enabled: true
    baseline-on-migrate: true
    validate-on-migrate: true
    locations: classpath:db
    table: schema_history
    baseline-version: 0
    encoding: UTF-8
    placeholder-replacement: true
    placeholders:
      app.name: ${spring.application.name}

mybatis-plus:
  configuration:
    local-cache-scope: statement
    cache-enabled: false
    # 打印SQL日志（开发环境）
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    banner: false
    db-config:
      id-type: auto

liteflow:
  rule-source: flow/flow.xml
  print-banner: false

app:
  cache:
    provider: two-level
  auth:
    secret_key: 'VIUFm9I5DGARzcnBgsVm0dVkncRC3sqJx4HkHfWW'
    redis_token_prefix: 'auth:token:'
  oss:
    provider: s3
    local:
      path: upload
      base_url: http://127.0.0.1:8080/upload/
    s3:
      access_key: 'b6nzKZdpw1VfcUOAerXx'
      secret_key: 'VIUFm9I5DGARzcnBgsVm0dVkncRC3sqJx4HkHfWW'
      endpoint: 'http://127.0.0.1:9000'
      bucket: 'ruson'
  audit:
    enabled: true
    async:
      max_pool_size: 36
  sms:
    provider: log
  domain:
    field-encrypt:
      provider: aes
      aes:
        secret_key: 'uk83YUQQ5UMxxGnBzpeibQuQYl2flMcyGlIWwVSqUVg='
  cors:
    enabled: true
    allowed-origins:
      - '*'
    allowed-methods: [ "GET", "POST", "PUT", "DELETE", "OPTIONS" ]
    allowed-headers: [ "*" ]
    allow-credentials: true
    max-age: 3600
  module:
    sys:
      default-password: admin