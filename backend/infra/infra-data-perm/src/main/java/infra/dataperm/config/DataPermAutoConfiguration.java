package infra.dataperm.config;

import infra.dataperm.core.DataPermHandler;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;

@Slf4j
@AutoConfiguration
@ConditionalOnClass(MybatisPlusInterceptor.class)
public class DataPermAutoConfiguration {
    @Bean
    @ConditionalOnMissingBean
    public DataPermHandler infraDataPermHandler() {
        log.info("[配置] 创建数据权限处理器");
        return new DataPermHandler();
    }

    @Bean
    @ConditionalOnMissingBean(MybatisPlusInterceptor.class)
    public MybatisPlusInterceptor infraDataPermInterceptor(DataPermHandler dataPermHandler) {
        log.info("[配置] 创建MyBatis-Plus拦截器并注入数据权限处理器");

        // 添加数据权限处理器
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        interceptor.addInnerInterceptor(new com.baomidou.mybatisplus.extension.plugins.inner.DataPermissionInterceptor(dataPermHandler));

        return interceptor;
    }

    @Bean
    public DataPermMyBatisPlusInterceptorEnhancer infraDataPermInterceptorEnhancer(DataPermHandler dataPermHandler) {
        return new DataPermMyBatisPlusInterceptorEnhancer(dataPermHandler);
    }
}
