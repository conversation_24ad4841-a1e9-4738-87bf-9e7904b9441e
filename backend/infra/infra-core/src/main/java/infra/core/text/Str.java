package infra.core.text;

import infra.core.exception.SerializationException;

import java.io.*;
import java.util.Base64;

/**
 * 字符串处理
 */
public final class Str {
    /**
     * 判断字符串是否为空
     *
     * @param str 字符串
     * @return 是否为空
     */
    public static boolean isEmpty(String str) {
        return str == null || str.isBlank();
    }

    /**
     * 如果字符串值为null，则返回空字串，否则返回原始字串。
     *
     * @param str 字符串
     * @return str
     */
    public static String nullToEmpty(String str) {
        return str == null ? "" : str;
    }

    /**
     * 如果字符串值为null，则返回指定默认值，否则返回原始字串。
     *
     * @param str 字符串
     * @return str
     */
    public static String nullTo(String str, String defaultValue) {
        return str == null ? defaultValue : str;
    }

    /**
     * 对象转base64
     *
     * @param obj 对象
     * @return base64
     */
    public static String base64Encode(Serializable obj) {
        if (obj == null) {
            return null;
        }

        try (ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
             ObjectOutputStream objectOutputStream = new ObjectOutputStream(byteArrayOutputStream)) {

            objectOutputStream.writeObject(obj);
            byte[] bytes = byteArrayOutputStream.toByteArray();
            return Base64.getEncoder().encodeToString(bytes);

        } catch (IOException e) {
            throw new SerializationException("序列化失败", e);
        }
    }

    /**
     * base64转对象
     *
     * @param base64 base64
     * @return 对象
     */
    public static Object base64Decode(String base64) {
        if (isEmpty(base64)) {
            return null;
        }

        try (ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(Base64.getDecoder().decode(base64));
             ObjectInputStream objectInputStream = new ObjectInputStream(byteArrayInputStream)) {
            return objectInputStream.readObject();
        } catch (IOException | ClassNotFoundException | ClassCastException e) {
            throw new SerializationException("反序列化失败", e);
        }
    }
}
