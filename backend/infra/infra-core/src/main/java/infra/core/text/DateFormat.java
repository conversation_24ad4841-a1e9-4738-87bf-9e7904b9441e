package infra.core.text;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;

/**
 * 日期时间格式化（使用 UTC+8 时区）
 */
public final class DateFormat {
    // UTC+8时区
    public static final ZoneId UTC8_ZONE = ZoneId.of("UTC+8");
    // 定义标准格式化器
    public static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    public static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    public static final DateTimeFormatter DATE_SHORT_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");

    /**
     * 将 Date 格式化为完整日期时间字符串（基于 UTC+8 时区）
     */
    public static String formatDateTime(Date date) {
        if (date == null) return null;
        return toUtc8LocalDateTime(date).format(DATE_TIME_FORMATTER);
    }

    /**
     * 将 LocalDateTime 格式化为完整日期时间字符串（直接转换，不涉及时区）
     */
    public static String formatDateTime(LocalDateTime dateTime) {
        if (dateTime == null) return null;
        return dateTime.format(DATE_TIME_FORMATTER);
    }

    /**
     * 将 Date 格式化为日期字符串（基于 UTC+8 时区）
     */
    public static String formatDate(Date date) {
        if (date == null) return null;
        return toUtc8LocalDateTime(date).format(DATE_FORMATTER);
    }

    /**
     * 将 LocalDateTime 格式化为日期字符串（直接转换，不涉及时区）
     */
    public static String formatDate(LocalDateTime dateTime) {
        if (dateTime == null) return null;
        return dateTime.format(DATE_FORMATTER);
    }

    /**
     * 将 LocalDate 格式化为日期字符串（直接转换）
     */
    public static String formatDate(LocalDate date) {
        if (date == null) return null;
        return date.format(DATE_FORMATTER);
    }

    /**
     * 将 Date 格式化为无秒级时间字符串（基于 UTC+8 时区）
     */
    public static String formatDateShortTime(Date date) {
        if (date == null) return null;
        return toUtc8LocalDateTime(date).format(DATE_SHORT_TIME_FORMATTER);
    }

    /**
     * 将 LocalDateTime 格式化为无秒级时间字符串（直接转换）
     */
    public static String formatDateShortTime(LocalDateTime dateTime) {
        if (dateTime == null) return null;
        return dateTime.format(DATE_SHORT_TIME_FORMATTER);
    }

    /**
     * 将 Instant 转换为 LocalDateTime
     */
    public static LocalDateTime ofInstant(Instant instant) {
        return LocalDateTime.ofInstant(instant, UTC8_ZONE);
    }

    /**
     * 转换 Date 为 UTC+8 的 LocalDateTime
     */
    private static LocalDateTime toUtc8LocalDateTime(Date date) {
        return date.toInstant().atZone(UTC8_ZONE).toLocalDateTime();
    }
}
