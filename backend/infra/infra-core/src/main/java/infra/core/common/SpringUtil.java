package infra.core.common;

import lombok.Getter;
import lombok.Setter;
import org.springframework.context.ApplicationContext;

/**
 * Spring工具
 */
public final class SpringUtil {
    /**
     * 设置ApplicationContext（由CommonAutoConfig调用）
     */
    @Setter
    @Getter
    private static ApplicationContext applicationContext;

    /**
     * 根据 Bean 名称获取 Bean 实例
     *
     * @param name Bean 名称
     * @return Bean 实例
     */
    public static Object getBean(String name) {
        return applicationContext.getBean(name);
    }

    /**
     * 根据 Bean 类型获取 Bean 实例
     *
     * @param clazz Bean 类型
     * @param <T>   泛型类型
     * @return Bean 实例
     */
    public static <T> T getBean(Class<T> clazz) {
        return applicationContext.getBean(clazz);
    }

    /**
     * 根据 Bean 名称和类型获取 Bean 实例
     *
     * @param name  Bean 名称
     * @param clazz Bean 类型
     * @param <T>   泛型类型
     * @return Bean 实例
     */
    public static <T> T getBean(String name, Class<T> clazz) {
        return applicationContext.getBean(name, clazz);
    }

    /**
     * 判断 Bean 是否存在
     *
     * @param name Bean 名称
     * @return 如果存在返回 true，否则返回 false
     */
    public static boolean containsBean(String name) {
        return applicationContext.containsBean(name);
    }

    /**
     * 判断 Bean 是否为单例
     *
     * @param name Bean 名称
     * @return 如果是单例返回 true，否则返回 false
     */
    public static boolean isSingleton(String name) {
        return applicationContext.isSingleton(name);
    }

    /**
     * 获取 Bean 的类型
     *
     * @param name Bean 名称
     * @return Bean 的类型
     */
    public static Class<?> getType(String name) {
        return applicationContext.getType(name);
    }

    /**
     * 发布事件
     *
     * @param event 事件对象
     */
    public static void publishEvent(Object event) {
        applicationContext.publishEvent(event);
    }
}
