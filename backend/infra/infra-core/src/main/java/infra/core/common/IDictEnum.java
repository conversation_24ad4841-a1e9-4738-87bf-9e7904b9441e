package infra.core.common;

import com.fasterxml.jackson.annotation.JsonValue;

import java.io.Serializable;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 字典枚举接口
 */
public interface IDictEnum {
    /**
     * 获取枚举值
     */
    @JsonValue
    int getValue();

    /**
     * 获取枚举描述
     */
    String getDesc();

    /**
     * 获取枚举项列表
     */
    static List<DictEnumItem> getItems(Class<? extends Enum<? extends IDictEnum>> enumClass) {
        return Arrays.stream(enumClass.getEnumConstants())
                .map(item -> {
                    IDictEnum dictEnum = (IDictEnum) item;
                    return new DictEnumItem(dictEnum.getValue(), dictEnum.getDesc());
                })
                .collect(Collectors.toList());
    }

    /**
     * 枚举项
     */
    record DictEnumItem(int id, String desc) implements Serializable {
    }
}
