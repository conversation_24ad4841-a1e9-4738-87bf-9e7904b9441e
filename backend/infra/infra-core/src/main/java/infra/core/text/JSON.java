package infra.core.text;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import infra.core.exception.SerializationException;
import lombok.Getter;
import org.springframework.util.ConcurrentReferenceHashMap;

import java.io.IOException;
import java.io.InputStream;
import java.io.Writer;
import java.lang.reflect.Type;
import java.util.Map;

/**
 * Json处理
 */
public final class JSON {
    // 提供给外部共享，可以统一序列化格式
    @Getter
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    private static final Map<Type, JavaType> TYPE_CACHE = new ConcurrentReferenceHashMap<>(64, ConcurrentReferenceHashMap.ReferenceType.WEAK);

    static {
        OBJECT_MAPPER.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
                .configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false)
                .configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false)
                .registerModule(new JavaTimeModule());
    }

    /**
     * 对象转JSON字符串（支持泛型）
     */
    public static <T> String toJson(T obj) {
        try {
            return OBJECT_MAPPER.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            throw new SerializationException("Json序列化失败", e);
        }
    }

    /**
     * 对象转JSON字节数组（支持泛型）
     */
    public static byte[] toBytes(Object obj) {
        try {
            return OBJECT_MAPPER.writeValueAsBytes(obj);
        } catch (JsonProcessingException e) {
            throw new SerializationException("Json序列化失败", e);
        }
    }

    /**
     * 直接向一个Writer中写入JSON
     */
    public static void writeTo(Object obj, Writer writer) {
        try {
            OBJECT_MAPPER.writeValue(writer, obj);
        } catch (IOException e) {
            throw new SerializationException("Json写入失败", e);
        }
    }


    /**
     * JSON字符串转对象（基础类型）
     */
    public static <T> T fromJson(String json, Class<T> clazz) {
        try {
            return OBJECT_MAPPER.readValue(json, clazz);
        } catch (JsonProcessingException e) {
            throw new SerializationException("Json反序列化失败", e);
        }
    }

    /**
     * JSON字符串转泛型对象（支持List/Map等复杂结构）
     */
    public static <T> T fromJson(String json, TypeReference<T> typeRef) {
        try {
            return OBJECT_MAPPER.readValue(json, typeRef);
        } catch (JsonProcessingException e) {
            throw new SerializationException("Json反序列化失败", e);
        }
    }

    /**
     * 高性能类型转换（带缓存优化）‌
     */
    public static <T> T fromJson(String json, Type type) {
        try {
            JavaType javaType = TYPE_CACHE.computeIfAbsent(type,
                    t -> OBJECT_MAPPER.getTypeFactory().constructType(t));
            return OBJECT_MAPPER.readValue(json, javaType);
        } catch (JsonProcessingException e) {
            throw new SerializationException("Json反序列化失败", e);
        }
    }

    /**
     * 流式处理（适用于大文件场景）‌
     */
    public static <T> T fromJson(InputStream input, Class<T> clazz) {
        try {
            return OBJECT_MAPPER.readValue(input, clazz);
        } catch (IOException e) {
            throw new SerializationException("Json反序列化失败", e);
        }
    }

    /**
     * 对象转JSON字节数组（支持泛型）
     */
    public static <T> T fromBytes(byte[] bytes, Class<T> clazz) {
        try {
            return OBJECT_MAPPER.readValue(bytes, clazz);
        } catch (IOException e) {
            throw new SerializationException("Json反序列化失败", e);
        }
    }

    /**
     * 类型转换
     */
    public static <T> T to(Object source, Class<T> clazz) {
        try {
            return OBJECT_MAPPER.convertValue(source, clazz);
        } catch (IllegalArgumentException e) {
            throw new SerializationException("类型转换失败", e);
        }
    }
}
