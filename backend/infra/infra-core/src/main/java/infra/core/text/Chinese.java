package infra.core.text;

/**
 * 中文相关
 */
public final class Chinese {
    /**
     * 汉字拼音
     *
     * @param pinyin 全拼
     * @param py     简拼
     */
    public record Pinyin(String pinyin, String py) {
    }

    private static final Pinyin PINYIN_EMPTY = new Pinyin("", "");

    /**
     * 将输入的字符串转换为拼音
     *
     * @param str 输入的字符串
     * @return 汉字拼音
     */
    public static Pinyin getPinyin(String str) {
        if (Str.isEmpty(str)) {
            return PINYIN_EMPTY;
        }

        StringBuilder py = new StringBuilder();
        StringBuilder pinyin = new StringBuilder();

        for (char c : str.toCharArray()) {
            String p = com.github.promeg.pinyinhelper.Pinyin.toPinyin(c).toLowerCase();
            char firstChar = p.charAt(0);
            py.append(firstChar);
            pinyin.append(Character.toUpperCase(firstChar));
            pinyin.append(p.substring(1));
        }

        return new Pinyin(pinyin.toString(), py.toString());
    }
}
