package infra.core.common;

import java.io.Serializable;

/**
 * 通用响应结果包装类
 * <p>
 * 用于统一封装API接口的返回结果，包含状态码、消息和数据。
 * 状态码0表示成功，非0表示失败。
 *
 * @param <T> 数据载荷类型
 */
public record Result<T>(int code, String msg, T data) implements Serializable {
    /**
     * 成功结果的单例对象
     * <p>
     * 用于无数据返回的成功场景
     */
    public static final Result<Void> OK = new Result<>(0, "", null);

    /**
     * 成功结果的单例对象
     * <p>
     * 用于无数据返回的成功场景
     */
    public static final Result<String> OK_STRING = new Result<>(0, "", null);

    /**
     * 成功结果的单例对象
     * <p>
     * 用于无数据返回的成功场景
     */
    public static final Result<Long> OK_LONG = new Result<>(0, "", null);

    /**
     * 失败结果的单例对象
     * <p>
     * 用于通用失败场景
     */
    public static final Result<Void> FAIL = new Result<>(-1, "操作失败", null);

    /**
     * 失败结果的单例对象
     * <p>
     * 用于通用失败场景
     */
    public static final Result<String> FAIL_STRING = new Result<>(-1, "操作失败", "");

    /**
     * 失败结果的单例对象
     * <p>
     * 用于通用失败场景
     */
    public static final Result<Long> FAIL_LONG = new Result<>(-1, "操作失败", null);

    /**
     * 创建带消息和数据的成功结果
     *
     * @param msg  成功提示消息
     * @param data 返回的数据
     * @param <T>  数据类型
     * @return 成功的响应结果
     */
    public static <T> Result<T> ok(String msg, T data) {
        return new Result<>(0, msg, data);
    }

    /**
     * 创建只带消息的成功结果
     *
     * @param msg 成功提示消息
     * @param <T> 数据类型
     * @return 成功的响应结果
     */
    public static <T> Result<T> okMsg(String msg) {
        return new Result<>(0, msg, null);
    }

    /**
     * 创建只带数据的成功结果
     *
     * @param data 返回的数据
     * @param <T>  数据类型
     * @return 成功的响应结果
     */
    public static <T> Result<T> okData(T data) {
        return new Result<>(0, "", data);
    }

    /**
     * 创建完整的失败结果
     *
     * @param code 错误码
     * @param msg  错误消息
     * @param data 相关数据
     * @param <T>  数据类型
     * @return 失败的响应结果
     */
    public static <T> Result<T> fail(int code, String msg, T data) {
        return new Result<>(code, msg, null);
    }

    /**
     * 创建带错误码和消息的失败结果
     *
     * @param code 错误码
     * @param msg  错误消息
     * @param <T>  数据类型
     * @return 失败的响应结果
     */
    public static <T> Result<T> fail(int code, String msg) {
        return new Result<>(code, msg, null);
    }

    /**
     * 创建带消息的通用失败结果
     * <p>
     * 使用默认错误码-1
     *
     * @param msg 错误消息
     * @param <T> 数据类型
     * @return 失败的响应结果
     */
    public static <T> Result<T> fail(String msg) {
        return new Result<>(-1, msg, null);
    }

    /**
     * 创建带消息和数据的通用失败结果
     * <p>
     * 使用默认错误码-1
     *
     * @param msg  错误消息
     * @param data 相关数据
     * @param <T>  数据类型
     * @return 失败的响应结果
     */
    public static <T> Result<T> fail(String msg, T data) {
        return new Result<>(-1, msg, data);
    }

    /**
     * 判断响应是否成功
     * 约定 >= 0时为成功，小于0时为失败
     *
     * @return true表示成功（code>=0），false表示失败（code<0）
     */
    public boolean success() {
        return code >= 0;
    }
}