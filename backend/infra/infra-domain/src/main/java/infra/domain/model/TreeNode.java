package infra.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 树节点
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TreeNode implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 节点唯一标识
     */
    private String key;

    /**
     * 节点ID
     */
    private Long id;

    /**
     * 节点名称
     */
    private String name;

    /**
     * 节点路径
     */
    private String path;

    /**
     * 节点数据
     */
    private Map<String, Object> data;

    /**
     * 子节点列表
     */
    private List<TreeNode> children;
}
