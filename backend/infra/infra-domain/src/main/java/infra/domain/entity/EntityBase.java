package infra.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.*;

import java.io.Serial;
import java.time.LocalDateTime;

/**
 * 含基础信息的实体
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public abstract class EntityBase extends IdEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    // 创建时间
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    // 创建人
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    // 创建人名称
    @TableField(fill = FieldFill.INSERT)
    private String createByName;

    // 修改时间
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    // 修改人
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    // 修改人名称
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateByName;
}