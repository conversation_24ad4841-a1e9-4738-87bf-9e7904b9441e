package infra.domain.handler;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import infra.auth.core.IUser;
import infra.auth.web.UserContext;
import infra.domain.entity.EntityBase;
import infra.domain.entity.IPermEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;

import java.time.LocalDateTime;

@Slf4j
public class UserInfoMetaHandler implements MetaObjectHandler {
    @Override
    public void insertFill(MetaObject metaObject) {
        IUser currentUser = UserContext.getCurrentUser();
        Object originalObject = metaObject.getOriginalObject();
        LocalDateTime now = LocalDateTime.now();

        // 检查是否为EntityBase的子类，填充基础审计字段
        if (originalObject instanceof EntityBase) {
            this.setFieldValByName("createTime", now, metaObject);
            this.setFieldValByName("updateTime", now, metaObject);
            if (currentUser != null) {
                this.setFieldValByName("createBy", currentUser.getId(), metaObject);
                this.setFieldValByName("createByName", currentUser.getUserName(), metaObject);
                this.setFieldValByName("updateBy", currentUser.getId(), metaObject);
                this.setFieldValByName("updateByName", currentUser.getUserName(), metaObject);
            }
        }

        // 检查是否实现了IPermEntity接口，填充数据权限字段
        if (originalObject instanceof IPermEntity) {
            if (currentUser != null) {
                this.setFieldValByName("userId", currentUser.getId(), metaObject);
                this.setFieldValByName("userName", currentUser.getUserName(), metaObject);
                this.setFieldValByName("deptId", currentUser.getDeptId(), metaObject);
                this.setFieldValByName("deptName", currentUser.getDeptName(), metaObject);
            }
        }
    }


    @Override
    public void updateFill(MetaObject metaObject) {
        IUser currentUser = UserContext.getCurrentUser();
        LocalDateTime now = LocalDateTime.now();

        // 只填充更新相关字段
        if (metaObject.getOriginalObject() instanceof EntityBase) {
            this.setFieldValByName("updateTime", now, metaObject);
            if (currentUser != null) {
                this.setFieldValByName("updateBy", currentUser.getId(), metaObject);
                this.setFieldValByName("updateByName", currentUser.getUserName(), metaObject);
            }
        }
    }
}
