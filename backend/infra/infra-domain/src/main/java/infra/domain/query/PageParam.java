package infra.domain.query;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;

import java.io.Serializable;

/**
 * 分页查询参数
 */
public record PageParam(
        @Schema(description = "页码")
        int page,

        @Schema(description = "每页条数")
        @Max(value = 10000, message = "每页条数不能超过10000")
        int size
) implements Serializable  {
    public PageParam {
        page = Math.max(page, 1);
        size = size <= 0 ? 30 : size;
    }
}