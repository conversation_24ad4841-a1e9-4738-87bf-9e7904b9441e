package infra.domain.query;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import infra.core.text.Str;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ConcurrentReferenceHashMap;

import java.lang.reflect.Field;
import java.util.Map;

@Slf4j
public class QueryUtil {
    // 缓存
    private static final Map<Class<?>, Field[]> TYPE_FIELD_CACHE = new ConcurrentReferenceHashMap<>(64);

    /**
     * 添加查询条件
     */
    public static <T> QueryWrapper<T> appendCondition(QueryWrapper<T> wrapper, Object queryDto) {
        if (queryDto == null) {
            return wrapper;
        }

        Class<?> clazz = queryDto.getClass();
        Field[] fields = TYPE_FIELD_CACHE.computeIfAbsent(clazz, Class::getDeclaredFields);

        for (Field field : fields) {
            field.setAccessible(true);
            try {
                Object value = field.get(queryDto);
                if (value == null) {
                    continue;
                }

                ColumnFilter columnFilter = field.getAnnotation(ColumnFilter.class);
                String columnName = columnFilter == null || Str.isEmpty(columnFilter.columnName()) ? field.getName() : columnFilter.columnName();
                MatchType matchType = columnFilter == null ? MatchType.EQ : columnFilter.value();
                appendCondition(wrapper, columnName, value, matchType);

            } catch (IllegalAccessException e) {
                log.warn("构建查询条件获取字段值失败: {}", field.getName(), e);
            }
        }

        return wrapper;
    }

    /**
     * 添加查询条件
     */
    public static <T> QueryWrapper<T> appendCondition(QueryWrapper<T> wrapper, String columnName, Object value, MatchType matchType) {
        if (Str.isEmpty(columnName)) {
            return wrapper;
        }

        if (!isSafeColumnName(columnName)) {
            log.warn("添加条件被忽略，列名: {}", columnName);
            return wrapper;
        }

        switch (matchType) {
            case NE:
                wrapper.ne(columnName, value);
                break;
            case GT:
                wrapper.gt(columnName, value);
                break;
            case GE:
                wrapper.ge(columnName, value);
                break;
            case LT:
                wrapper.lt(columnName, value);
                break;
            case LE:
                wrapper.le(columnName, value);
                break;
            case LIKE:
                wrapper.like(columnName, value);
                break;
            case LLIKE:
                wrapper.likeLeft(columnName, value);
                break;
            case RLIKE:
                wrapper.likeRight(columnName, value);
                break;
            case EQ:
            default:
                wrapper.eq(columnName, value);
        }

        return wrapper;
    }

    /**
     * 添加排序条件
     */
    public static <T> QueryWrapper<T> appendOrder(QueryWrapper<T> wrapper, OrderParam orderParam) {
        if (orderParam == null) {
            return wrapper;
        }

        String sortBy = orderParam.sort();
        if (!Str.isEmpty(sortBy)) {
            if (!isSafeColumnName(sortBy)) {
                log.warn("排序字段被忽略，字段：{}", sortBy);
                return wrapper;
            }
            wrapper.orderBy(true, orderParam.asc(), sortBy);
        }

        return wrapper;
    }

    /**
     * 判断是否为一个安全的列名，即只包含字母、数字及下划线，防止注入
     */
    private static boolean isSafeColumnName(String input) {
        if (Str.isEmpty(input)) {
            return false;
        }

        // 第一个字符必须是字母或下划线
        char firstChar = input.charAt(0);
        if (!Character.isLetter(firstChar) && firstChar != '_') {
            return false;
        }

        int length = input.length();
        for (int i = 1; i < length; i++) {
            char c = input.charAt(i);
            if (!Character.isLetterOrDigit(c) && c != '_') {
                return false;
            }
        }

        return true;
    }
}
