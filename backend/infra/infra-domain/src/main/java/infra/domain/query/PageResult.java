package infra.domain.query;

import infra.core.common.ObjectUtil;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Collection;

/**
 * 分页结果
 *
 * @param <T>
 */
public record PageResult<T>(
        @Schema(description = "分页数据")
        Collection<T> items,

        @Schema(description = "总记录数")
        Long total
) {
    /**
     * 转换为另一个类型的分页结果
     */
    public <Target> PageResult<Target> to(Class<Target> targetClass) {
        return new PageResult<>(items == null ? null : items.stream().map(value ->
                ObjectUtil.copyTo(value, ObjectUtil.newInstance(targetClass))
        ).toList(), total);
    }
}
