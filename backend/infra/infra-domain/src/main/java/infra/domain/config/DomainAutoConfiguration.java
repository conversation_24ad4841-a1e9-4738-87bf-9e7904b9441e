package infra.domain.config;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.OptimisticLockerInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import infra.core.exception.ConfigException;
import infra.domain.encrypt.AesFieldEncryptor;
import infra.domain.encrypt.FieldEncryptInterceptor;
import infra.domain.encrypt.IFieldEncryptor;
import infra.domain.encrypt.Sm4FieldEncryptor;
import infra.domain.handler.UserInfoMetaHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;

@Slf4j
@AutoConfiguration
@ConditionalOnClass(MybatisPlusInterceptor.class)
@EnableConfigurationProperties(DomainConfigProperties.class)
public class DomainAutoConfiguration {
    @Bean
    @ConditionalOnMissingBean(MybatisPlusInterceptor.class)
    public MybatisPlusInterceptor infraDomainInterceptor(
            @Value("${mybatis-plus.global-config.db-config.db-type:postgresql}") String dbType,
            IFieldEncryptor fieldEncryptor
    ) {
        log.info("[配置] 创建MyBatis-Plus拦截器及分页和乐观锁插件，数据库类型:{}", dbType);

        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        // 分页插件
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.getDbType(dbType)));
        // 乐观锁插件
        interceptor.addInnerInterceptor(new OptimisticLockerInnerInterceptor());

        return interceptor;
    }

    @Bean
    public DomainMyBatisPlusInterceptorEnhancer infraDomainInterceptorEnhancer(@Value("${mybatis-plus.global-config.db-config.db-type:postgresql}") String dbType) {
        return new DomainMyBatisPlusInterceptorEnhancer(dbType);
    }

    @Bean
    @ConditionalOnMissingBean(MetaObjectHandler.class)
    public UserInfoMetaHandler infraUserInfoMetaHandler() {
        log.info("[配置] 创建用户信息元数据填充程序");
        return new UserInfoMetaHandler();
    }

    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnProperty(prefix = "app.domain.field-encrypt", name = "provider")
    public IFieldEncryptor infraFieldEncrypt(DomainConfigProperties config) {
        var fieldEncrypt = config.getFieldEncrypt();
        var provider = fieldEncrypt.getProvider();

        log.info("[配置] 创建字段加密处理器：{}", provider);

        if ("aes".equals(provider)) {
            return new AesFieldEncryptor(fieldEncrypt.getAes().getSecretKey());
        } else if ("sm4".equals(provider)) {
            return new Sm4FieldEncryptor(fieldEncrypt.getSm4().getSecretKey());
        } else {
            throw new ConfigException("不支持的字段加密处理器" + provider);
        }
    }

    @Bean
    @ConditionalOnBean({IFieldEncryptor.class})
    public FieldEncryptInterceptor infraFieldEncryptInterceptor(IFieldEncryptor fieldEncryptor) {
        log.info("[配置] 创建字段加密拦截器");
        return new FieldEncryptInterceptor(fieldEncryptor);
    }

}
