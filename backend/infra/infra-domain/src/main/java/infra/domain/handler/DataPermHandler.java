package infra.domain.handler;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import infra.auth.annotation.DataPerm;
import infra.auth.web.UserContext;
import infra.domain.entity.IPermEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.List;

/**
 * 数据权限处理
 */
@Slf4j
public class DataPermHandler {
    /**
     * 判断是否为一个需要数据权限验证的类型
     */
    public static <T> boolean isDataPermType(Class<T> entityClass) {
        return IPermEntity.class.isAssignableFrom(entityClass);
    }

    /**
     * 应用数据权限过滤
     *
     * @param wrapper 查询包装器
     * @param <T>     实体类型
     * @return 应用权限过滤后的查询包装器
     */
    public static <T extends IPermEntity> QueryWrapper<T> applyFilter(QueryWrapper<T> wrapper) {
        var user = UserContext.getCurrentUser();
        // 未登录用户无权限访问数据
        if (user == null) {
            return wrapper.apply("1 = 0");
        }

        // 管理员或拥有全部权限
        if (user.isAdmin() || user.getDataPerm() == DataPerm.ALL) {
            return wrapper;
        }

        // 根据数据权限类型应用过滤
        return switch (user.getDataPerm()) {
            case SELF -> applySelfFilter(wrapper, user.getId());
            case DEPT -> applyDeptFilter(wrapper, user.getDeptId());
            case DEPT_CHILD -> applyDeptChildFilter(wrapper, user.getDeptAndChildren());
            default -> wrapper.apply("1 = 0");
        };
    }

    /**
     * 应用数据权限过滤
     *
     * @param wrapper     查询包装器
     * @param entityClass 实体类
     * @param <T>         实体类型
     * @return 应用权限过滤后的查询包装器
     */
    @SuppressWarnings("unchecked")
    public static <T> QueryWrapper<T> applyFilter(QueryWrapper<T> wrapper, Class<?> entityClass) {
        // 检查实体是否继承自PermEntity
        if (!isDataPermType(entityClass)) {
            return wrapper;
        }

        // 运行时类型安全说明：
        // 1. 已通过isAssignableFrom确认entityClass是IPermEntity的子类
        // 2. QueryWrapper<T>中的T必然是IPermEntity的子类
        // 3. 因此类型转换在运行时是安全的
        return (QueryWrapper<T>) applyFilter((QueryWrapper<? extends IPermEntity>) wrapper);
    }

    /**
     * 判断是否有指定实体的数据权限
     */
    public static <T extends IPermEntity> boolean hasDataPerm(T entity) {
        if (entity == null) {
            throw new IllegalArgumentException("实体不能为空");
        }

        var user = UserContext.getCurrentUser();
        if (user == null) {
            return false;
        }

        // 管理员或拥有全部权限
        if (user.isAdmin() || user.getDataPerm() == DataPerm.ALL) {
            return true;
        }

        // 根据数据权限类型判断
        return switch (user.getDataPerm()) {
            case SELF -> hasSelfPerm(entity, user.getId());
            case DEPT -> hasDeptPerm(entity, user.getDeptId());
            case DEPT_CHILD -> hasDeptChildPerm(entity, user.getDeptAndChildren());
            default -> false;
        };
    }

    /**
     * 批量判断实体操作权限
     */
    public static <T extends IPermEntity> boolean hasDataPerm(Collection<T> entities) {
        if (CollectionUtils.isEmpty(entities)) {
            return true;
        }

        var user = UserContext.getCurrentUser();
        if (user == null) {
            return false;
        }

        // 管理员或拥有全部权限
        if (user.isAdmin() || user.getDataPerm() == DataPerm.ALL) {
            return true;
        }

        // 批量验证
        for (T entity : entities) {
            boolean hasPermission = switch (user.getDataPerm()) {
                case SELF -> hasSelfPerm(entity, user.getId());
                case DEPT -> hasDeptPerm(entity, user.getDeptId());
                case DEPT_CHILD -> hasDeptChildPerm(entity, user.getDeptAndChildren());
                default -> false;
            };

            if (!hasPermission) {
                return false;
            }
        }

        return true;
    }

    /**
     * 验证权限，无权限时抛异常
     */
    public static <T extends IPermEntity> void validateDataPerm(T entity) {
        if (!hasDataPerm(entity)) {
            throw new SecurityException("没有该数据的权限");
        }
    }

    /**
     * 验证权限，无权限时抛异常
     */
    public static <T extends IPermEntity> void validateDataPerm(Collection<T> entities) {
        if (!hasDataPerm(entities)) {
            throw new SecurityException("没有该数据的权限");
        }
    }

    /**
     * 验证权限，无权限时抛异常
     */
    public static <T> void validateDataPerm(T entity) {
        if (entity instanceof IPermEntity permEntity) {
            validateDataPerm(permEntity);
        }
    }

    /**
     * 验证权限，无权限时抛异常
     */
    @SuppressWarnings("unchecked")
    public static <T> void validateDataPerm(Collection<T> entities, Class<?> entityClass) {
        if (isDataPermType(entityClass)) {
            validateDataPerm((Collection<? extends IPermEntity>) entities);
        }
    }

    /**
     * 应用个人数据过滤
     */
    private static <T extends IPermEntity> QueryWrapper<T> applySelfFilter(QueryWrapper<T> wrapper, Long userId) {
        if (userId == null) {
            return wrapper.apply("1 = 0");
        }
        return wrapper.eq("user_id", userId);
    }

    /**
     * 应用部门数据过滤
     */
    private static <T extends IPermEntity> QueryWrapper<T> applyDeptFilter(QueryWrapper<T> wrapper, Long deptId) {
        if (deptId == null) {
            return wrapper.apply("1 = 0");
        }
        return wrapper.eq("dept_id", deptId);
    }

    /**
     * 应用部门及子部门数据过滤
     */
    private static <T extends IPermEntity> QueryWrapper<T> applyDeptChildFilter(QueryWrapper<T> wrapper, List<Long> deptIds) {
        if (deptIds == null || deptIds.isEmpty()) {
            return wrapper.apply("1 = 0");
        }
        return wrapper.in("dept_id", deptIds);
    }

    /**
     * 判断个人数据权限
     */
    private static <T extends IPermEntity> boolean hasSelfPerm(T entity, Long userId) {
        return userId != null && userId.equals(entity.getUserId());
    }

    /**
     * 判断部门数据权限
     */
    private static <T extends IPermEntity> boolean hasDeptPerm(T entity, Long deptId) {
        return deptId != null && deptId.equals(entity.getDeptId());
    }

    /**
     * 判断部门及子部门数据权限
     */
    private static <T extends IPermEntity> boolean hasDeptChildPerm(T entity, List<Long> deptIds) {
        return deptIds != null && deptIds.contains(entity.getDeptId());
    }
}
