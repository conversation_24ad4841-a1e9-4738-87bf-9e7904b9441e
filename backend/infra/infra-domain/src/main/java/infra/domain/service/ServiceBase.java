package infra.domain.service;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import infra.domain.entity.IdEntity;
import infra.domain.entity.IPermEntity;
import infra.domain.handler.DataPermHandler;
import infra.domain.query.OrderParam;
import infra.domain.query.PageParam;
import infra.domain.query.PageResult;
import infra.domain.query.QueryUtil;
import lombok.extern.slf4j.Slf4j;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ResolvableType;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;

/**
 * 基础服务类
 * 提供实体的基本CRUD操作
 * 在查询时提供了数据权限过滤的统一方法，在更新及删除时考虑可能存在各种跨Service相互调用，
 * 所以没有做数据权限判断，故提供了相关的方法可以有必需要的地方手动判断。
 *
 * @param <M> Mapper类型
 * @param <T> 实体类型，必须继承IdEntity
 */
@Slf4j
public abstract class ServiceBase<M extends BaseMapper<T>, T extends IdEntity> implements IService {
    private Class<T> entityClass;

    // ==================== 受保存方法，可以在子类中调用或重写 ====================

    /**
     * 受保护的Repository，子类必要时可访问
     */
    @Autowired
    protected BaseMapper<T> mapper;

    /**
     * 业务过滤器，用于默认业务条件过滤
     * 子类可覆盖此方法来定义业务过滤逻辑
     */
    protected Function<QueryWrapper<T>, QueryWrapper<T>> bizFilter() {
        return wrapper -> wrapper;
    }

    /**
     * 默认排序，子类可覆盖此方法来自定义默认排序逻辑
     */
    protected Function<QueryWrapper<T>, QueryWrapper<T>> defaultOrderBy() {
        return wrapper -> wrapper.orderByDesc("id");
    }

    /**
     * 验证钩子方法，子类可覆盖
     * 可做基本实体的验证逻辑
     */
    protected void validateEntity(T entity, boolean isUpdate) {
    }

    // ==================== 公用基础方法 ====================

    /**
     * 获取实体类型
     */
    @SuppressWarnings("unchecked")
    public Class<T> getEntityClass() {
        if (this.entityClass == null) {
            ResolvableType resolvableType = ResolvableType.forClass(this.getClass());
            ResolvableType superType = resolvableType.getSuperType();
            ResolvableType[] generics = superType.getGenerics();
            if (generics.length >= 2) {
                this.entityClass = (Class<T>) generics[1].resolve();
            }
        }

        return this.entityClass;
    }

    /**
     * 获取查询包装器
     */
    public QueryWrapper<T> getQuery(FilterOptions filterOptions) {
        return getQuery(filterOptions, true);
    }

    /**
     * 获取查询包装器
     */
    public QueryWrapper<T> getQuery(FilterOptions filterOptions, boolean appendDefaultOrder) {
        QueryWrapper<T> wrapper = new QueryWrapper<>();

        // 禁用业务过滤
        if (filterOptions == null || !filterOptions.disableBizFilter()) wrapper = bizFilter().apply(wrapper);

        // 禁用数据权限过滤
        if (filterOptions == null || !filterOptions.disableDataPermFilter()) {
            wrapper = DataPermHandler.applyFilter(wrapper, getEntityClass());
        }

        // 添加默认排序规则
        if (appendDefaultOrder) {
            wrapper = defaultOrderBy().apply(wrapper);
        }

        return wrapper;
    }

    /**
     * 判断指定实体是否有数据权限
     */
    public boolean hasDataPerm(T entity) {
        if (entity instanceof IPermEntity permEntity) {
            return DataPermHandler.hasDataPerm(permEntity);
        }
        return true;
    }

    /**
     * 对指定实体验证数据权限，无权限时抛异常
     */
    public void validateDataPerm(T entity) {
        DataPermHandler.validateDataPerm(entity);
    }

    /**
     * 验证数据权限，无权限时抛异常
     */
    public void validateDataPerm(Collection<T> entities) {
        DataPermHandler.validateDataPerm(entities, getEntityClass());
    }

    /**
     * 验证数据权限，无权限时抛异常
     */
    public void validateDataPermByIds(Collection<Long> ids) {
        if (DataPermHandler.isDataPermType(getEntityClass())) {
            List<T> entities = getListByIds(ids, FilterOptions.DISABLE_ALL_FILTER);
            validateDataPerm(entities);
        }
    }

    // ==================== 增删改 ====================

    /**
     * 新增实体
     */
    public boolean add(T entity) {
        if (entity == null) {
            throw new IllegalArgumentException("新增数据不能为空");
        }

        validateEntity(entity, false);
        entity.setId(null);

        return mapper.insert(entity) > 0;
    }

    /**
     * 批量新增
     */
    public boolean addBatch(Collection<T> entities) {
        return addBatch(entities, 1000);
    }

    /**
     * 批量新增
     */
    public boolean addBatch(Collection<T> entities, int batchSize) {
        if (CollectionUtils.isEmpty(entities)) {
            return true;
        }

        // 清空所有实体的ID+验证
        entities.forEach(entity -> {
            entity.setId(null);
            validateEntity(entity, false);
        });

        mapper.insert(entities, batchSize);
        return true;
    }

    /**
     * 更新实体
     */
    public boolean update(T entity) {
        if (entity == null) {
            throw new IllegalArgumentException("更新数据不能为空");
        }

        validateEntity(entity, true);

        return mapper.updateById(entity) > 0;
    }

    /**
     * 批量更新
     */
    public boolean updateBatch(Collection<T> entities) {
        return updateBatch(entities, 1000);
    }

    /**
     * 批量更新
     */
    public boolean updateBatch(Collection<T> entities, int batchSize) {
        if (CollectionUtils.isEmpty(entities)) {
            return true;
        }

        // 验证实体
        entities.forEach(entity -> validateEntity(entity, true));

        mapper.updateById(entities, batchSize);
        return true;
    }

    /**
     * 按条件批量更新实体
     * 注意此种更新不会进行实体验证，仅在明确不需要验证并只更新部分字段时使用
     */
    public boolean updateBatch(Consumer<UpdateWrapper<T>> updateWrapper) {
        UpdateWrapper<T> wrapper = new UpdateWrapper<>();
        updateWrapper.accept(wrapper);
        if (wrapper.isEmptyOfWhere()) {
            throw new IllegalArgumentException("更新条件不能为空");
        }

        return mapper.update(wrapper) > 0;
    }


    /**
     * 按条件更新实体中的字段
     * 注意此种更新不会进行实体验证，仅在明确不需要验证并只更新部分字段时使用
     */
    public boolean updateBatch(T entity, Consumer<UpdateWrapper<T>> updateWrapper) {
        UpdateWrapper<T> wrapper = new UpdateWrapper<>();
        updateWrapper.accept(wrapper);
        return mapper.update(entity, wrapper) > 0;
    }

    /**
     * 删除实体
     */
    public boolean delete(T entity) {
        if (entity == null) {
            throw new IllegalArgumentException("删除数据不能为空");
        }

        return mapper.deleteById(entity) > 0;
    }

    /**
     * 删除实体
     */
    public boolean deleteById(Long id) {
        if (id == null || id <= 0) {
            throw new IllegalArgumentException("删除数据id无效");
        }

        return mapper.deleteById(id) > 0;
    }

    /**
     * 批量删除
     */
    public boolean deleteBatch(Collection<T> entities) {
        if (CollectionUtils.isEmpty(entities)) {
            return true;
        }
        return mapper.deleteByIds(entities) > 0;
    }

    /**
     * 批量删除
     */
    public boolean deleteByIds(Collection<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return true;
        }
        return mapper.deleteByIds(ids) > 0;
    }

    /**
     * 批量删除
     */
    public boolean deleteBatch(Consumer<QueryWrapper<T>> condition) {
        QueryWrapper<T> queryWrapper = new QueryWrapper<>();
        condition.accept(queryWrapper);
        mapper.delete(queryWrapper);
        return true;
    }

    // ==================== 查询 ====================

    /**
     * 根据ID获取实体
     */
    public Optional<T> getById(Long id) {
        return getById(id, FilterOptions.DEFAULTS);
    }

    /**
     * 根据ID获取实体
     */
    public Optional<T> getById(Long id, FilterOptions filterOptions) {
        if (id == null || id <= 0) {
            return Optional.empty();
        }

        var wrapper = getQuery(filterOptions, false);
        wrapper.eq("id", id);

        return Optional.ofNullable(mapper.selectOne(wrapper));
    }

    /**
     * 获取第一个符合条件的实体
     */
    public Optional<T> getFirst(Consumer<QueryWrapper<T>> condition) {
        return getFirst(condition, FilterOptions.DEFAULTS);
    }

    /**
     * 获取第一个符合条件的实体
     */
    public Optional<T> getFirst(Consumer<QueryWrapper<T>> condition, FilterOptions filterOptions) {
        var wrapper = getQuery(filterOptions);
        if (condition != null) {
            condition.accept(wrapper);
        }
        return Optional.ofNullable(mapper.selectOne(wrapper));
    }

    /**
     * 获取列表
     */
    public List<T> getList() {
        return getList(null, FilterOptions.DEFAULTS);
    }

    /**
     * 获取列表
     */
    public List<T> getList(FilterOptions filterOptions) {
        return getList(null, filterOptions);
    }

    /**
     * 获取列表
     */
    public List<T> getList(Consumer<QueryWrapper<T>> condition) {
        return getList(condition, FilterOptions.DEFAULTS);
    }

    /**
     * 获取列表
     */
    public List<T> getList(Consumer<QueryWrapper<T>> condition, FilterOptions filterOptions) {
        var wrapper = getQuery(filterOptions);
        if (condition != null) {
            condition.accept(wrapper);
        }
        return mapper.selectList(wrapper);
    }

    /**
     * 获取指定数量的列表
     */
    public List<T> getList(Consumer<QueryWrapper<T>> condition, int limit) {
        return getList(condition, limit, FilterOptions.DEFAULTS);
    }

    /**
     * 获取指定数量的列表
     */
    public List<T> getList(Consumer<QueryWrapper<T>> condition, int limit, FilterOptions filterOptions) {
        var wrapper = getQuery(filterOptions);
        if (condition != null) {
            condition.accept(wrapper);
        }
        Page<T> page = new Page<>(1, limit, false);
        Page<T> result = mapper.selectPage(page, wrapper);
        return result.getRecords();
    }

    /**
     * 根据ID列表获取实体列表
     */
    public List<T> getListByIds(Collection<Long> ids) {
        return getListByIds(ids, FilterOptions.DEFAULTS);
    }

    /**
     * 根据ID列表获取实体列表
     */
    public List<T> getListByIds(Collection<Long> ids, FilterOptions filterOptions) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }

        var wrapper = getQuery(filterOptions);
        wrapper.in("id", ids);
        return mapper.selectList(wrapper);
    }

    /**
     * 获取符合指定条件的id集合
     */
    public List<Long> getIds(Consumer<QueryWrapper<T>> condition) {
        return getIds(condition, FilterOptions.DEFAULTS);
    }

    /**
     * 获取符合指定条件的id集合
     */
    public List<Long> getIds(Consumer<QueryWrapper<T>> condition, FilterOptions filterOptions) {
        var wrapper = getQuery(filterOptions);
        if (condition != null) {
            condition.accept(wrapper);
        }
        wrapper.select("id");
        return mapper.selectObjs(wrapper).stream().map(o -> ((Number) o).longValue()).toList();
    }

    /**
     * 统计数量
     */
    public long count() {
        return count(null, FilterOptions.DEFAULTS);
    }

    /**
     * 统计数量
     */
    public long count(Consumer<QueryWrapper<T>> condition) {
        return count(condition, FilterOptions.DEFAULTS);
    }

    /**
     * 统计数量
     */
    public long count(Consumer<QueryWrapper<T>> condition, FilterOptions filterOptions) {
        var wrapper = getQuery(filterOptions, false);
        if (condition != null) {
            condition.accept(wrapper);
        }
        return mapper.selectCount(wrapper);
    }

    /**
     * 是否存在
     */
    public boolean exists(Consumer<QueryWrapper<T>> condition) {
        return exists(condition, FilterOptions.DEFAULTS);
    }

    /**
     * 是否存在
     */
    public boolean exists(Consumer<QueryWrapper<T>> condition, FilterOptions filterOptions) {
        return count(condition, filterOptions) > 0;
    }

    /**
     * 分页查询
     */
    public PageResult<T> getPage(PageParam pageParam) {
        return getPage(null, pageParam, FilterOptions.DEFAULTS);
    }

    /**
     * 分页查询
     */
    public PageResult<T> getPage(Consumer<QueryWrapper<T>> condition, PageParam pageParam) {
        return getPage(condition, pageParam, FilterOptions.DEFAULTS);
    }

    /**
     * 分页查询
     */
    public PageResult<T> getPage(Consumer<QueryWrapper<T>> condition, PageParam pageParam, FilterOptions filterOptions) {
        if (pageParam == null) {
            throw new IllegalArgumentException("分页参数不能为空");
        }

        var wrapper = getQuery(filterOptions);
        if (condition != null) {
            condition.accept(wrapper);
        }

        // 分页查询数据
        var page = new Page<T>(pageParam.page(), pageParam.size());
        var pageResult = mapper.selectPage(page, wrapper);

        return new PageResult<>(pageResult.getRecords(), pageResult.getTotal());
    }

    /**
     * 简单dto分页查询
     */
    public PageResult<T> getPage(Object queryDto, PageParam pageParam) {
        return getPage(queryDto, pageParam, OrderParam.ID_DESC, FilterOptions.DEFAULTS);
    }

    /**
     * 简单dto分页查询
     */
    public PageResult<T> getPage(Object queryDto, PageParam pageParam, FilterOptions filterOptions) {
        return getPage(queryDto, pageParam, OrderParam.ID_DESC, filterOptions);
    }

    /**
     * 简单dto分页查询
     */
    public PageResult<T> getPage(Object queryDto, PageParam pageParam, OrderParam orderParam) {
        return getPage(queryDto, pageParam, orderParam, FilterOptions.DEFAULTS);
    }

    /**
     * 简单dto分页查询
     */
    public PageResult<T> getPage(Object queryDto, PageParam pageParam, OrderParam orderParam, FilterOptions filterOptions) {
        if (pageParam == null) {
            throw new IllegalArgumentException("分页参数不能为空");
        }

        return getPage(wrapper -> {
            // 添加查询条件
            if (queryDto != null) {
                QueryUtil.appendCondition(wrapper, queryDto);
            }

            // 处理排序
            if (orderParam != null) {
                QueryUtil.appendOrder(wrapper, orderParam);
            }
        }, pageParam, filterOptions);
    }

    /**
     * 流式查询
     */
    public void streamQuery(Consumer<QueryWrapper<T>> condition, Consumer<T> processor) {
        streamQuery(condition, processor, FilterOptions.DEFAULTS);
    }

    /**
     * 流式查询
     */
    public void streamQuery(Consumer<QueryWrapper<T>> condition, Consumer<T> processor, FilterOptions filterOptions) {
        var wrapper = getQuery(filterOptions);
        if (condition != null) {
            condition.accept(wrapper);
        }

        mapper.selectList(wrapper, resultContext -> {
            T entity = resultContext.getResultObject();
            processor.accept(entity);
        });
    }

    /**
     * 批量流式查询 - 按批次收集数据后处理
     */
    public void streamQueryBatch(Consumer<QueryWrapper<T>> condition, int batchSize, Consumer<List<T>> batchProcessor) {
        streamQueryBatch(condition, batchSize, batchProcessor, FilterOptions.DEFAULTS);
    }

    /**
     * 批量流式查询 - 按批次收集数据后处理
     */
    public void streamQueryBatch(Consumer<QueryWrapper<T>> condition, int batchSize, Consumer<List<T>> batchProcessor, FilterOptions filterOptions) {
        List<T> batch = new ArrayList<>(batchSize);

        streamQuery(condition, entity -> {
            batch.add(entity);
            if (batch.size() >= batchSize) {
                batchProcessor.accept(new ArrayList<>(batch));
                batch.clear();
            }
        }, filterOptions);

        // 处理最后一批数据
        if (!batch.isEmpty()) {
            batchProcessor.accept(batch);
        }
    }
}

