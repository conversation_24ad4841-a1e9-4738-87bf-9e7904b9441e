package infra.domain.entity;

/**
 * 权限实体接口
 */
public interface IPermEntity extends IEntity {
    /**
     * 获取用户ID
     *
     * @return 用户ID
     */
    Long getUserId();

    /**
     * 设置用户ID
     */
    void setUserId(Long userId);

    /**
     * 获取用户名称
     *
     * @return 用户名称
     */
    String getUserName();

    /**
     * 设置用户名称
     */
    void setUserName(String userName);

    /**
     * 获取部门ID
     *
     * @return 部门ID
     */
    Long getDeptId();

    /**
     * 设置部门ID
     */
    void setDeptId(Long deptId);

    /**
     * 获取部门名称
     *
     * @return 部门名称
     */
    String getDeptName();

    /**
     * 设置部门名称
     */
    void setDeptName(String deptName);
}
