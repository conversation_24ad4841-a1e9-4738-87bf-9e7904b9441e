package infra.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.*;

import java.io.Serial;

/**
 * 包含权限归属数据的实体
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public abstract class PermEntity extends EntityBase implements IPermEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    // 权限归属用户ID
    @TableField(fill = FieldFill.INSERT)
    private Long userId;

    // 权限归属用户名称
    @TableField(fill = FieldFill.INSERT)
    private String userName;

    // 权限归属部门ID
    @TableField(fill = FieldFill.INSERT)
    private Long deptId;

    // 权限归属部门名称
    @TableField(fill = FieldFill.INSERT)
    private String deptName;
}
