package infra.domain.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import infra.core.exception.BizException;
import infra.domain.entity.TreeEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 树形结构服务基类
 */
public abstract class TreeService<M extends BaseMapper<T>, T extends TreeEntity> extends ServiceBase<M, T> {
    /**
     * 新增树节点
     */
    @Transactional
    @Override
    public boolean add(T node) {
        if (node == null) {
            throw new IllegalArgumentException("新增数据不能为空");
        }

        // 验证父节点并设置初始路径
        String initialPath = "";

        if (node.getParentId() != null && node.getParentId() > 0) {
            Optional<T> parent = getById(node.getParentId(), FilterOptions.DISABLE_ALL_FILTER);
            if (parent.isEmpty()) {
                throw new IllegalArgumentException("父节点不存在");
            }
            initialPath = parent.get().getPath();
        }

        if (!super.add(node)) return false;

        // 更新路径加入自身ID
        node.setPath(initialPath + node.getId() + ",");
        if (mapper.updateById(node) <= 0) throw new BizException("更新数据路径失败");
        return true;
    }


    /**
     * 更新树节点
     */
    @Transactional
    @Override
    public boolean update(T node) {
        if (node == null) {
            throw new IllegalArgumentException("更新数据不能为空");
        }

        Optional<T> oldNodeOpt = getById(node.getId(), FilterOptions.DISABLE_ALL_FILTER);
        if (oldNodeOpt.isEmpty()) {
            throw new IllegalArgumentException("更新数据不存在");
        }
        T oldNode = oldNodeOpt.get();

        if (Objects.equals(node.getParentId(), node.getId())) {
            throw new IllegalArgumentException("父级不能为自己");
        }

        // 如果父节点有变化
        if (!Objects.equals(oldNode.getParentId(), node.getParentId())) {
            // 检查新父节点
            if (node.getParentId() != null && node.getParentId() > 0) {
                Optional<T> parentOpt = getById(node.getParentId(), FilterOptions.DISABLE_ALL_FILTER);
                if (parentOpt.isEmpty()) {
                    throw new IllegalArgumentException("父节点不存在");
                }

                T parent = parentOpt.get();
                // 检查是否将节点移动到其子节点下
                if (parent.getPath().startsWith(oldNode.getPath())) {
                    throw new IllegalArgumentException("不能将节点移动到其子节点下");
                }

                // 更新当前节点路径
                node.setPath(parent.getPath() + node.getId() + ",");
            } else {
                // 移到根节点
                node.setPath(node.getId() + ",");
            }

            // 更新所有子节点的路径
            List<T> children = getList(wrapper -> wrapper.likeRight("path", oldNode.getPath()), FilterOptions.DISABLE_ALL_FILTER);
            String newPrefix = node.getPath();
            int oldPrefixLength = oldNode.getPath().length();

            for (T child : children) {
                if (!child.getId().equals(node.getId())) {
                    child.setPath(newPrefix + child.getPath().substring(oldPrefixLength));
                    if (mapper.updateById(child) <= 0) throw new BizException("更新子节点失败");
                }
            }
        }

        if (!super.update(node)) throw new BizException("更新节点失败");
        return true;
    }

    /**
     * 删除节点
     */
    @Override
    public boolean delete(T node) {
        if (node == null) {
            throw new IllegalArgumentException("删除数据不能为空");
        }

        // 获取所有子节点(含自身)
        return mapper.delete(new QueryWrapper<T>().likeRight("path", node.getPath())) > 0;
    }

    /**
     * 删除节点
     */
    @Override
    public boolean deleteById(Long id) {
        var node = getById(id);
        return node.filter(this::delete).isPresent();
    }

    /**
     * 批量删除
     */
    @Transactional
    @Override
    public boolean deleteByIds(Collection<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return true;
        }

        var removedList = getListByIds(ids);
        if (CollectionUtils.isEmpty(removedList)) {
            return true;
        }

        var removePaths = removedList.stream().map(TreeEntity::getPath).toList();
        return removedList.stream().allMatch(this::delete);
    }

    /**
     * 获取所有直接子节点
     */
    public List<T> getChildren(Long nodeId) {
        return getChildren(nodeId, FilterOptions.DEFAULTS);
    }

    /**
     * 获取所有直接子节点
     */
    public List<T> getChildren(Long nodeId, FilterOptions filterOptions) {
        if (nodeId == null) {
            return Collections.emptyList();
        }

        return getList(wrapper -> wrapper.eq("parent_id", nodeId), filterOptions);
    }

    /**
     * 获取所有子孙节点
     */
    public List<T> getAllChildren(Long nodeId, boolean includeSelf) {
        return getAllChildren(nodeId, includeSelf, FilterOptions.DEFAULTS);
    }

    /**
     * 获取所有子孙节点
     */
    public List<T> getAllChildren(Long nodeId, boolean includeSelf, FilterOptions filterOptions) {
        if (nodeId == null) {
            return Collections.emptyList();
        }

        Optional<T> nodeOpt = getById(nodeId, filterOptions);
        if (nodeOpt.isEmpty()) {
            return Collections.emptyList();
        }

        T node = nodeOpt.get();
        List<T> children = getList(wrapper -> wrapper.likeRight("path", node.getPath()), filterOptions);

        if (!includeSelf) {
            children.removeIf(child -> child.getId().equals(nodeId));
        }

        return children;
    }

    /**
     * 获取所有子孙节点Id
     */
    public List<Long> getAllChildrenIds(Long nodeId, boolean includeSelf) {
        return getAllChildrenIds(nodeId, includeSelf, FilterOptions.DEFAULTS);
    }

    /**
     * 获取所有子孙节点Id
     */
    public List<Long> getAllChildrenIds(Long nodeId, boolean includeSelf, FilterOptions filterOptions) {
        if (nodeId == null) {
            return Collections.emptyList();
        }

        Optional<T> nodeOpt = getById(nodeId, filterOptions);
        if (nodeOpt.isEmpty()) {
            return Collections.emptyList();
        }

        T node = nodeOpt.get();
        List<Long> children = getIds(wrapper -> wrapper.likeRight("path", node.getPath()), filterOptions);

        if (!includeSelf) {
            children.removeIf(child -> child.equals(nodeId));
        }

        return children;
    }

    /**
     * 获取父节点
     */
    public Optional<T> getParent(Long nodeId) {
        return getParent(nodeId, FilterOptions.DEFAULTS);
    }

    /**
     * 获取父节点
     */
    public Optional<T> getParent(Long nodeId, FilterOptions filterOptions) {
        if (nodeId == null) {
            return Optional.empty();
        }

        Optional<T> nodeOpt = getById(nodeId, filterOptions);
        if (nodeOpt.isEmpty() || nodeOpt.get().getParentId() == null) {
            return Optional.empty();
        }

        return getById(nodeOpt.get().getParentId(), filterOptions);
    }

    /**
     * 获取所有父节点（祖先节点）
     */
    public List<T> getParents(Long nodeId, boolean includeSelf) {
        return getParents(nodeId, includeSelf, FilterOptions.DEFAULTS);
    }

    /**
     * 获取所有父节点（祖先节点）
     */
    public List<T> getParents(Long nodeId, boolean includeSelf, FilterOptions filterOptions) {
        if (nodeId == null) {
            return Collections.emptyList();
        }

        Optional<T> nodeOpt = getById(nodeId, filterOptions);
        if (nodeOpt.isEmpty()) {
            return Collections.emptyList();
        }

        T node = nodeOpt.get();
        String path = node.getPath();

        if (path == null || path.isEmpty()) {
            return includeSelf ? List.of(node) : Collections.emptyList();
        }

        // 解析路径获取所有父节点ID
        List<Long> parentIds = Arrays.stream(path.split(",")).filter(s -> !s.isEmpty()).map(Long::parseLong).toList();

        if (!includeSelf) {
            parentIds.removeLast(); // 移除自身ID
        }

        if (parentIds.isEmpty()) {
            return Collections.emptyList();
        }

        return getListByIds(parentIds, filterOptions);
    }

    /**
     * 构建树形结构
     */
    public List<T> buildTree(Long topId) {
        return buildTree(topId, FilterOptions.DEFAULTS);
    }

    /**
     * 构建树形结构
     */
    public List<T> buildTree(Long topId, FilterOptions filterOptions) {
        List<T> allNodes;

        if (topId != null && topId > 0) {
            // 获取指定节点的所有子节点
            allNodes = getAllChildren(topId, false, filterOptions);
        } else {
            // 获取所有节点
            allNodes = getList(filterOptions);
        }

        return buildTree(allNodes, topId);
    }

    /**
     * 从节点列表构建树形结构
     * nodes 为最顶级的一层结构
     */
    private List<T> buildTree(List<T> nodes, Long parentId) {
        Map<Long, List<T>> childrenMap = nodes.stream().collect(Collectors.groupingBy(node -> node.getParentId() != null ? node.getParentId() : 0L));

        List<T> rootNodes = childrenMap.getOrDefault(parentId != null ? parentId : 0L, Collections.emptyList());

        // 递归构建子树
        for (T node : rootNodes) {
            List<T> children = buildChildrenTree(node.getId(), childrenMap);
            node.setChildren(children);
        }

        // 按排序字段排序
        rootNodes.sort((a, b) -> {
            int orderCompare = Integer.compare(b.getSort(), a.getSort());
            return orderCompare != 0 ? orderCompare : Long.compare(a.getId(), b.getId());
        });

        return rootNodes;
    }

    /**
     * 递归构建子树
     */
    private List<T> buildChildrenTree(Long parentId, Map<Long, List<T>> childrenMap) {
        List<T> children = childrenMap.getOrDefault(parentId, Collections.emptyList());

        for (T child : children) {
            List<T> grandChildren = buildChildrenTree(child.getId(), childrenMap);
            child.setChildren(grandChildren);
        }

        // 按排序字段排序
        children.sort((a, b) -> {
            int orderCompare = Integer.compare(b.getSort(), a.getSort());
            return orderCompare != 0 ? orderCompare : Long.compare(a.getId(), b.getId());
        });

        return children;
    }
}
