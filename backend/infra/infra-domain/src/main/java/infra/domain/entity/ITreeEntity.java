package infra.domain.entity;

/**
 * 树形结构实体接口
 */
public interface ITreeEntity extends IEntity {
    /**
     * 获取父级ID
     */
    Long getParentId();

    /**
     * 设置父级ID
     */
    void setParentId(Long parentId);

    /**
     * 获取路径
     */
    String getPath();

    /**
     * 设置路径
     */
    void setPath(String path);

    /**
     * 获取排序值
     */
    Integer getSort();

    /**
     * 设置排序值
     */
    void setSort(Integer sort);
}
