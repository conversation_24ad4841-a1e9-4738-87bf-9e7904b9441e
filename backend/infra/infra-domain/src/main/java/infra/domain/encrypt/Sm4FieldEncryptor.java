package infra.domain.encrypt;

import infra.core.exception.BizException;
import infra.core.exception.ConfigException;
import infra.core.security.Sm4;
import infra.core.text.Str;
import lombok.extern.slf4j.Slf4j;

/**
 * SM4加解密器
 */
@Slf4j
public class Sm4FieldEncryptor implements IFieldEncryptor {
    private final String secret;

    public Sm4FieldEncryptor(String secret) {
        if (Str.isEmpty(secret))
            throw new ConfigException("SM4加密secret配置不能为空");
        this.secret = secret;
    }

    @Override
    public String encrypt(String plainText) {
        if (Str.isEmpty(plainText)) return plainText;

        try {
            return Sm4.encryptDeterministic(plainText, secret);
        } catch (Exception e) {
            log.error("SM4加密失败: {}", plainText, e);
            throw new BizException("数据加密失败");
        }
    }

    @Override
    public String decrypt(String cipherText) {
        if (Str.isEmpty(cipherText)) return cipherText;

        try {
            return Sm4.decrypt(cipherText, secret);
        } catch (Exception e) {
            log.error("SM4解密失败: {}", cipherText, e);
            throw new BizException("数据解密失败");
        }
    }
}
