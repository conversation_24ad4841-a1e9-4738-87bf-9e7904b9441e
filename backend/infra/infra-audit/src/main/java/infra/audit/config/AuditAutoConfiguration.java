package infra.audit.config;

import infra.audit.core.AuditLogAspect;
import infra.audit.core.IAuditLogger;
import infra.audit.logger.Slf4JAuditLogger;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.core.task.TaskExecutor;
import org.springframework.scheduling.annotation.AsyncConfigurer;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 审计自动配置
 */
@Slf4j
@AutoConfiguration
@EnableConfigurationProperties(AuditConfigProperties.class)
@ConditionalOnProperty(name = "app.audit.enabled", havingValue = "true")
@EnableAspectJAutoProxy
@EnableAsync
public class AuditAutoConfiguration {
    /**
     * 创建审计专用线程池
     */
    @Bean("auditTaskExecutor")
    @ConditionalOnMissingBean(name = "auditTaskExecutor")
    public TaskExecutor auditTaskExecutor(AuditConfigProperties config) {
        var poolConfig = config.getAsync();
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(poolConfig.getPoolSize());
        executor.setMaxPoolSize(poolConfig.getMaxPoolSize());
        executor.setQueueCapacity(poolConfig.getQueueCapacity());
        executor.setThreadNamePrefix("audit-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        log.info("[配置] 创建审计专用线程池 - auditTaskExecutor {}", poolConfig);
        return executor;
    }

    /**
     * 配置审计专用线程池
     */
    @Bean
    @ConditionalOnMissingBean(AsyncConfigurer.class)
    public AsyncConfigurer asyncConfigurer(AuditConfigProperties config) {
        return new AsyncConfigurer() {
            @Override
            public Executor getAsyncExecutor() {
                return auditTaskExecutor(config);
            }
        };
    }

    /*
     * 创建审计日志切面
     */
    @Bean
    public AuditLogAspect infraAuditLogAspect(IAuditLogger auditLogger, AuditConfigProperties config) {
        log.info("[配置] 审计日志切入");
        return new AuditLogAspect(auditLogger, config);
    }

    /**
     * 创建审计日志记录器
     */
    @Bean
    @ConditionalOnMissingBean(IAuditLogger.class)
    public IAuditLogger infraSlf4JAuditLogger() {
        log.info("[配置] 创建默认审计日志记录器 - Slf4JAuditLogger");
        return new Slf4JAuditLogger();
    }
}
