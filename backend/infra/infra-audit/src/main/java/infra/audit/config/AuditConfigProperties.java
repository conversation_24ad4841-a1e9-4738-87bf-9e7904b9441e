package infra.audit.config;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

/**
 * 审计配置属性
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Validated
@ConfigurationProperties(prefix = "app.audit")
public class AuditConfigProperties {
    // 是否启用审计功能
    private boolean enabled;
    // 异步处理配置
    private AsyncConfig async = new AsyncConfig();

    /**
     * 异步处理配置
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AsyncConfig {
        // 线程池大小(默认2)
        private int poolSize = 2;
        // 最大线程数(默认16)
        private int maxPoolSize = 16;
        // 线程队列大小(默认100)
        private int queueCapacity = 100;
    }
}
