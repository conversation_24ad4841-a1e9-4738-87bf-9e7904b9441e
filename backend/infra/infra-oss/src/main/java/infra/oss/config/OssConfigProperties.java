package infra.oss.config;

import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

/**
 * OSS配置属性
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Validated
@ConfigurationProperties(prefix = "app.oss")
public class OssConfigProperties {
    // 存储类型, s3, local
    private String provider;
    // S3配置
    private S3Config s3 = new S3Config();
    // 本地配置
    private LocalConfig local = new LocalConfig();

    /**
     * S3配置
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class S3Config {
        // 访问域名
        private String endpoint;
        // 访问Key
        private String accessKey;
        // 访问密钥
        private String secretKey;
        // 存储桶
        private String bucket;
        // 地域
        private String region;
    }

    /**
     * 本地配置
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LocalConfig {
        // 文件路径(本地时用)
        private String path;
        // 文件访问基础地址(本地时使用)
        private String baseUrl;
    }
}
