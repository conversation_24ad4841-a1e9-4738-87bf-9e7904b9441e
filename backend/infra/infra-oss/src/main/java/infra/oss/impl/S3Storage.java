package infra.oss.impl;

import infra.core.exception.ConfigException;
import infra.core.text.Str;
import infra.oss.config.OssConfigProperties;
import infra.oss.core.IStorage;

import infra.oss.core.StorageException;
import io.minio.*;
import io.minio.errors.ErrorResponseException;

import java.io.InputStream;
import java.io.OutputStream;

/**
 * S3存储实现
 */
public class S3Storage implements IStorage {
    private static final int DEFAULT_PART_SIZE = 10 * 1024 * 1024; // 10MB
    private static final String NO_SUCH_KEY = "NoSuchKey";

    private final String baseUrl;
    private final MinioClient minioClient;
    private final OssConfigProperties.S3Config config;

    public S3Storage(OssConfigProperties.S3Config config) {
        this.config = validateConfig(config);
        this.minioClient = createMinioClient();
        baseUrl = config.getEndpoint().replaceAll("/$", "");
        ensureBucketExists();
    }

    /**
     * 检查对象是否存在
     *
     * @param objectKey 对象键
     * @return true表示存在
     */
    @Override
    public boolean exists(String objectKey) {
        try {
            minioClient.statObject(
                    StatObjectArgs.builder()
                            .bucket(config.getBucket())
                            .object(objectKey)
                            .build()
            );
            return true;
        } catch (ErrorResponseException e) {
            if (NO_SUCH_KEY.equals(e.errorResponse().code())) {
                return false;
            }
            throw new StorageException("检查objectKey存在失败：" + objectKey, e);
        } catch (Exception e) {
            throw new StorageException("检查objectKey存在失败：" + objectKey, e);
        }
    }

    /**
     * 上传对象
     *
     * @param source    输入流
     * @param objectKey 对象键
     */
    @Override
    public void upload(InputStream source, String objectKey) {
        try {
            minioClient.putObject(
                    PutObjectArgs.builder()
                            .bucket(config.getBucket())
                            .object(objectKey)
                            .stream(source, -1, DEFAULT_PART_SIZE)
                            .build()
            );
        } catch (Exception e) {
            throw new StorageException("上传对象失败：" + objectKey, e);
        }
    }

    /**
     * 下载对象到输出流
     *
     * @param objectKey   对象键
     * @param destination 目标输出流
     */
    @Override
    public void download(String objectKey, OutputStream destination) {
        try (var inputStream = minioClient.getObject(
                GetObjectArgs.builder()
                        .bucket(config.getBucket())
                        .object(objectKey)
                        .build()
        )) {
            inputStream.transferTo(destination);
        } catch (Exception e) {
            throw new StorageException("下载对象失败：" + objectKey, e);
        }
    }

    /**
     * 获取对象输入流
     *
     * @param objectKey 对象键
     * @return 文件输入流
     */
    @Override
    public InputStream getInputStream(String objectKey) {
        try {
            return minioClient.getObject(
                    GetObjectArgs.builder()
                            .bucket(config.getBucket())
                            .object(objectKey)
                            .build()
            );
        } catch (Exception e) {
            throw new StorageException("获取文件流失败：" + objectKey, e);
        }
    }


    /**
     * 删除对象
     *
     * @param objectKey 对象键
     */
    @Override
    public void delete(String objectKey) {
        try {
            minioClient.removeObject(
                    RemoveObjectArgs.builder()
                            .bucket(config.getBucket())
                            .object(objectKey)
                            .build()
            );
        } catch (Exception e) {
            throw new StorageException("删除对象失败：" + objectKey, e);
        }
    }

    /**
     * 获取对象访问URL
     *
     * @param objectKey 对象键
     * @return 访问URL
     */
    @Override
    public String getUrl(String objectKey) {
        return baseUrl + "/" + config.getBucket() + "/" + objectKey;
    }

    /**
     * 获取对象大小
     *
     * @param objectKey 对象键
     * @return 大小（字节）
     */
    @Override
    public long getSize(String objectKey) {
        try {
            var stat = minioClient.statObject(
                    StatObjectArgs.builder()
                            .bucket(config.getBucket())
                            .object(objectKey)
                            .build()
            );
            return stat.size();
        } catch (Exception e) {
            throw new StorageException("获取对象大小失败：" + objectKey, e);
        }
    }

    /**
     * 校验配置
     */
    private OssConfigProperties.S3Config validateConfig(OssConfigProperties.S3Config config) {
        if (Str.isEmpty(config.getEndpoint())) {
            throw new ConfigException("endpoint配置不能为空");
        }
        if (Str.isEmpty(config.getAccessKey())) {
            throw new ConfigException("accessKey配置不能为空");
        }
        if (Str.isEmpty(config.getSecretKey())) {
            throw new ConfigException("secretKey配置不能为空");
        }
        if (Str.isEmpty(config.getBucket())) {
            throw new ConfigException("bucket配置不能为空");
        }
        return config;
    }

    /**
     * 创建MinioClient
     */
    private MinioClient createMinioClient() {
        var builder = MinioClient.builder()
                .endpoint(config.getEndpoint())
                .credentials(config.getAccessKey(), config.getSecretKey());

        if (!Str.isEmpty(config.getRegion())) {
            builder.region(config.getRegion());
        }

        return builder.build();
    }

    /**
     * 确保Bucket存在
     */
    private void ensureBucketExists() {
        try {
            boolean exists = minioClient.bucketExists(
                    BucketExistsArgs.builder().bucket(config.getBucket()).build()
            );
            if (!exists) {
                minioClient.makeBucket(
                        MakeBucketArgs.builder().bucket(config.getBucket()).build()
                );
            }
        } catch (Exception e) {
            throw new ConfigException("配置 bucket 不存在: " + config.getBucket(), e);
        }
    }
}
