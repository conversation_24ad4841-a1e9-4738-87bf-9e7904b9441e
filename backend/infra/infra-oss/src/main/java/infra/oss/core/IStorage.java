package infra.oss.core;

import java.io.InputStream;
import java.io.OutputStream;

/**
 * 文件存储接口
 */
public interface IStorage {
    /**
     * 检查对象是否存在
     * @param objectKey 对象键
     * @return true表示存在
     */
    boolean exists(String objectKey);

    /**
     * 上传对象
     * @param source 输入流
     * @param objectKey 对象键
     */
    void upload(InputStream source, String objectKey);

    /**
     * 下载对象到输出流
     * @param objectKey 对象键
     * @param destination 目标输出流
     */
    void download(String objectKey, OutputStream destination);

    /**
     * 获取对象输入流
     * @param objectKey 对象键
     * @return 文件输入流，调用方负责关闭流
     * @throws StorageException 当文件不存在或访问失败时抛出
     */
    InputStream getInputStream(String objectKey);

    /**
     * 删除对象
     * @param objectKey 对象键
     */
    void delete(String objectKey);

    /**
     * 获取对象访问URL
     * @param objectKey 对象键
     * @return 访问URL
     */
    String getUrl(String objectKey);

    /**
     * 获取对象大小
     * @param objectKey 对象键
     * @return 大小（字节）
     */
    long getSize(String objectKey);
}
