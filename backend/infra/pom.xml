<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>top</groupId>
        <artifactId>project</artifactId>
        <version>${revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <groupId>infra</groupId>
    <artifactId>infra</artifactId>
    <packaging>pom</packaging>

    <modules>
        <module>infra-ai</module>
        <module>infra-audit</module>
        <module>infra-auth</module>
        <module>infra-cache</module>
        <module>infra-core</module>
        <module>infra-domain</module>
        <module>infra-task</module>
        <module>infra-oss</module>
        <module>infra-mq</module>
        <module>infra-sms</module>
        <module>infra-data-perm</module>
        <module>infra-report</module>
        <module>infra-workflow</module>
    </modules>

    <dependencies>
        <!-- 基础依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-autoconfigure</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>jakarta.validation</groupId>
            <artifactId>jakarta.validation-api</artifactId>
            <scope>provided</scope>
        </dependency>
    </dependencies>
</project>