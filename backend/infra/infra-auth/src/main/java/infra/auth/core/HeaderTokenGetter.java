package infra.auth.core;

import infra.core.text.Str;
import jakarta.servlet.http.HttpServletRequest;
import lombok.AllArgsConstructor;

/**
 * 从请求头中获取token
 */
@AllArgsConstructor
public class HeaderTokenGetter implements ITokenGetter {
    private final String headerName;

    @Override
    public String getToken(HttpServletRequest request) {
        String header = request.getHeader(headerName);
        if (Str.isEmpty(header)) {
            return null;
        }

        if (header.startsWith("Bearer ")) {
            return header.substring(7);
        }
        return header;
    }
}