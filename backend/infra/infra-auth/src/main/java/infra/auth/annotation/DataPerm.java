package infra.auth.annotation;

import com.baomidou.mybatisplus.annotation.EnumValue;
import infra.core.common.IDictEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 数据权限枚举
 */
@Getter
@AllArgsConstructor
public enum DataPerm implements IDictEnum {
    SELF(0, "仅自己"),
    DEPT(1, "部门"),
    DEPT_CHILD(2, "部门及子部门"),
    ALL(3, "所有");

    @EnumValue
    // 数据权限类型值
    private final int value;
    // 数据权限类型名称
    private final String desc;

    public static DataPerm fromValue(long value) {
        for (DataPerm v : values()) {
            if (v.value == value) {
                return v;
            }
        }
        throw new IllegalArgumentException("无效的数据权限类型值: " + value);
    }
}
