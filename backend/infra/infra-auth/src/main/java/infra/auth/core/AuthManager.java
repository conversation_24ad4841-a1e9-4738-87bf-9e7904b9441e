package infra.auth.core;

import infra.auth.config.AuthConfigProperties;
import infra.core.text.Str;

import infra.core.web.ServletUtil;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.security.Keys;
import lombok.extern.slf4j.Slf4j;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.lang.Nullable;

import javax.crypto.SecretKey;
import java.util.Date;

/**
 * 认证管理
 */
@Slf4j
public class AuthManager {
    private final IUserService userService;
    private final ITokenGetter tokenGetter;
    private final IDeptGetter deptGetter;
    private final ITokenManager tokenManager;
    private final AuthConfigProperties config;
    private final SecretKey signingKey;

    public AuthManager(AuthConfigProperties config,
                       IUserService userService,
                       ITokenGetter tokenGetter,
                       IDeptGetter deptGetter,
                       @Nullable ITokenManager tokenManager) {
        this.config = config;
        this.userService = userService;
        this.tokenGetter = tokenGetter;
        this.deptGetter = deptGetter;
        this.tokenManager = tokenManager;
        this.signingKey = Keys.hmacShaKeyFor(config.getSecretKey().getBytes());
    }

    /**
     * 创建Token
     */
    public Token createToken(Long userId, String extra) {
        long now = System.currentTimeMillis() / 1000;

        String accessToken = createJwtToken(userId, extra, "access",
                now + config.getAccessTokenExpireSeconds());
        String refreshToken = createJwtToken(userId, extra, "refresh",
                now + config.getRefreshTokenExpireSeconds());

        Token token = new Token(accessToken, refreshToken, config.getAccessTokenExpireSeconds());
        if (tokenManager != null) {
            HttpServletRequest request = ServletUtil.getRequest();
            if (request != null) {
                tokenManager.createToken(userId, accessToken, refreshToken, request);
            }
        }

        return token;
    }

    /**
     * 刷新Token
     */
    public Token refreshAccessToken(String refreshToken) {
        if (Str.isEmpty(refreshToken)) {
            return null;
        }

        try {
            TokenPayload payload = decodeToken(refreshToken);
            if (!"refresh".equals(payload.type())) {
                return null;
            }

            String[] sub = payload.sub().split("-", 2);
            Long userId = Long.parseLong(sub[0]);
            String extra = sub.length > 1 ? sub[1] : "";

            HttpServletRequest request = ServletUtil.getRequest();
            Long deptId = request != null ? deptGetter.getDept(request) : null;

            IUser user = userService.getUser(userId, deptId, extra);
            if (user == null) {
                return null;
            }

            if (tokenManager != null && request != null &&
                    !tokenManager.useRefreshToken(userId, refreshToken, request)) {
                return null;
            }

            return createToken(userId, extra);

        } catch (Exception e) {
            log.info("刷新Token失败", e);
            return null;
        }
    }

    /**
     * 获取当前用户
     */
    public IUser getCurrentUser() {
        HttpServletRequest request = ServletUtil.getRequest();
        if (request == null) {
            return null;
        }

        String token = tokenGetter.getToken(request);
        if (Str.isEmpty(token)) {
            return null;
        }

        try {
            TokenPayload payload = decodeToken(token);
            if (!"access".equals(payload.type())) {
                return null;
            }

            String[] sub = payload.sub().split("-", 2);
            Long userId = Long.parseLong(sub[0]);
            String extra = sub.length > 1 ? sub[1] : "";

            if (tokenManager != null && !tokenManager.validAccessToken(userId, token, request)) {
                return null;
            }

            Long deptId = deptGetter.getDept(request);
            return userService.getUser(userId, deptId, extra);

        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 注销/登出
     */
    public void logout() {
        HttpServletRequest request = ServletUtil.getRequest();
        if (request == null) {
            return;
        }

        String token = tokenGetter.getToken(request);
        if (Str.isEmpty(token)) {
            return;
        }

        try {
            TokenPayload payload = decodeToken(token);
            if (!"access".equals(payload.type())) {
                return;
            }

            String[] sub = payload.sub().split("-", 2);
            Long userId = Long.parseLong(sub[0]);

            userService.logout(userId);
            if (tokenManager != null) {
                tokenManager.logout(userId, token);
            }

        } catch (Exception e) {
            log.info("登出失败", e);
        }
    }

    /**
     * 创建Token
     */
    private String createJwtToken(Long userId, String extra, String type, Long exp) {
        return Jwts.builder()
                .claim("type", type)
                .subject(userId + "-" + extra)
                .expiration(new Date(exp * 1000))
                .issuedAt(new Date())
                .signWith(signingKey)
                .compact();
    }

    /**
     * 解析Token
     */
    private TokenPayload decodeToken(String token) {
        Claims claims = Jwts.parser()
                .verifyWith(signingKey)
                .build()
                .parseSignedClaims(token)
                .getPayload();

        return new TokenPayload(
                claims.get("type", String.class),
                claims.getSubject(),
                claims.getExpiration().getTime() / 1000,
                claims.getIssuedAt().getTime() / 1000
        );
    }
}
