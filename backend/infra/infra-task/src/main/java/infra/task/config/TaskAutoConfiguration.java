package infra.task.config;

import infra.task.core.IJob;
import infra.task.core.IJobService;
import infra.task.core.TaskScheduler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Bean;
import org.springframework.scheduling.annotation.EnableScheduling;

@Slf4j
@EnableScheduling
@AutoConfiguration
public class TaskAutoConfiguration {
    /**
     * 任务调度
     */
    @Bean
    @ConditionalOnBean(IJobService.class)
    public TaskScheduler infraTaskScheduler(IJobService jobService) {
        log.info("[配置] 任务调度已启用");
        return new TaskScheduler(jobService);
    }

    /**
     * 任务注册监听，在启动后自动注册所有任务
     */
    @Bean
    @ConditionalOnBean({TaskScheduler.class, IJobService.class})
    public ApplicationListener<ApplicationReadyEvent> infraTaskRegistrationListener(
            ApplicationContext applicationContext,
            TaskScheduler taskScheduler) {
        return event -> {
            try {
                // 获取所有IJob类型的Bean
                var jobBeans = applicationContext.getBeansOfType(IJob.class);
                if (!jobBeans.isEmpty()) {
                    var jobList = jobBeans.values().stream().toList();
                    taskScheduler.registerJobs(jobList);
                    log.info("[配置] 成功注册 {} 个定时任务", jobList.size());
                } else {
                    log.info("[配置] 未发现任何定时任务");
                }
            } catch (Exception e) {
                log.error("[配置] 自动注册定时任务失败", e);
            }
        };
    }
}
