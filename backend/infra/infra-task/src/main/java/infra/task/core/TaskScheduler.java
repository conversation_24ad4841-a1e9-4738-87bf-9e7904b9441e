package infra.task.core;

import infra.core.text.DateFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

/**
 * 任务调度器
 * 负责管理和执行定时任务，支持多实例部署时的任务分布式执行
 */
@Slf4j
public class TaskScheduler {
    // 任务服务
    private final IJobService jobService;
    // 存储JobId-任务信息
    private final Map<String, JobInfo> jobs = new ConcurrentHashMap<>();
    // 使用虚拟线程执行器
    private final Executor virtualExecutor = Executors.newVirtualThreadPerTaskExecutor();

    public TaskScheduler(IJobService jobService) {
        this.jobService = jobService;
    }

    /**
     * 每秒检查一次任务
     */
    @Scheduled(fixedRate = 1000)
    public void mainLoop() {
        LocalDateTime now = LocalDateTime.now();

        for (JobInfo jobInfo : jobs.values()) {
            try {
                if (shouldExecute(jobInfo, now)) {
                    // 尝试开始执行任务（使用乐观锁）
                    log.debug("尝试开始执行任务: {}", jobInfo.getJob().getJobId());
                    JobRunFlag runFlag = jobService.tryStart(jobInfo.job.getJobId());

                    if (runFlag.nextRunTime() == null) {
                        // 执行任务
                        virtualExecutor.execute(() -> executeJob(jobInfo.job));
                    } else {
                        log.info("任务 {} 未就绪，下次尝试执行时间: {}", jobInfo.getJob().getJobId(), runFlag.nextRunTime());
                        // 更新本地缓存的下次执行时间
                        jobInfo.setNextRunTime(runFlag.nextRunTime());
                    }
                }
            } catch (Exception e) {
                log.error("处理任务 {} 时发生错误", jobInfo.getJob().getJobId(), e);
            }
        }
    }

    /**
     * 判断任务是否应该执行
     */
    private boolean shouldExecute(JobInfo jobInfo, LocalDateTime now) {
        return jobInfo.getNextRunTime() == null || jobInfo.getNextRunTime().isBefore(now) || jobInfo.getNextRunTime().isEqual(now);
    }

    /**
     * 执行单个任务
     */
    private void executeJob(IJob job) {
        LocalDateTime startTime = LocalDateTime.now();
        log.info("开始执行任务: {}", job.getJobId());

        String result;
        boolean success = false;

        try {
            result = job.execute();
            success = true;
            log.info("任务执行成功: {} 耗时 {}ms", job.getJobId(), java.time.Duration.between(startTime, LocalDateTime.now()).toMillis());
        } catch (Exception e) {
            result = e.getMessage();
            log.error("任务执行失败: {}", job.getJobId(), e);
        }

        try {
            // 更新任务执行结果
            JobRunFlag runFlag = jobService.runEnd(job.getJobId(), success, result);
            // 更新本地缓存
            jobs.computeIfPresent(job.getJobId(), (key, jobInfo) -> {
                jobInfo.setNextRunTime(runFlag.nextRunTime());
                return jobInfo;
            });
        } catch (Exception e) {
            log.error("更新任务执行结果失败: {}", job.getJobId(), e);
        }
    }

    /**
     * 注册任务
     */
    public void registerJobs(List<IJob> jobList) {
        if (jobList == null || jobList.isEmpty()) {
            return;
        }

        try {
            // 注册任务到数据库
            List<JobRunFlag> runFlags = jobService.register(jobList);
            // 更新本地
            for (JobRunFlag runFlag : runFlags) {
                jobList.stream().filter(item -> item.getJobId().equals(runFlag.jobId())).findFirst().ifPresent(iJob -> jobs.put(runFlag.jobId(), new JobInfo(iJob, runFlag.nextRunTime())));
            }

            if (log.isDebugEnabled()) {
                for (JobRunFlag flag : runFlags) {
                    log.debug("注册任务: {}，任务下次运行时间：{}", flag.jobId(), DateFormat.formatDateTime(flag.nextRunTime()));
                }
            }
        } catch (Exception e) {
            log.error("注册任务失败", e);
        }
    }

    /**
     * 手动执行任务
     */
    public void executeJobNow(String jobId) {
        var jobInfo = jobs.get(jobId);
        if (jobInfo != null) {
            virtualExecutor.execute(() -> executeJob(jobInfo.job));
            log.info("手动执行任务: {}", jobId);
        } else {
            log.warn("执行任务不存在: {}", jobId);
        }
    }

    /**
     * 任务信息对象，用于存储任务实例和下次执行时间
     */
    @Data
    @AllArgsConstructor
    public static class JobInfo {
        private IJob job;
        private volatile LocalDateTime nextRunTime;
    }
}
