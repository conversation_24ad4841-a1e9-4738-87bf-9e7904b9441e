package infra.task.core;

/**
 * 工作/任务执行器接口，定义定时任务必须实现的属性和方法
 */
public interface IJob {
    /**
     * 任务ID
     */
    String getJobId();

    /**
     * 任务描述
     */
    String getJobDesc();

    /**
     * 任务执行表达式
     */
    String getCron();

    /**
     * 任务执行表达式描述
     */
    String getCronDesc();

    /**
     * 排序值
     */
    Integer getSort();

    /**
     * 执行任务
     * @return 任务执行结果信息
     */
    String execute() throws Exception;
}
