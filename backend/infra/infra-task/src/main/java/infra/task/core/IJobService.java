package infra.task.core;

import java.util.List;

/**
 * 任务服务接口
 */
public interface IJobService {
    /**
     * 注册任务
     *
     * @param jobs 任务列表
     */
    List<JobRunFlag> register(List<IJob> jobs);

    /**
     * 尝试运行任务
     *
     * @param jobId 任务ID
     * @return 任务运行标志
     */
    JobRunFlag tryStart(String jobId);

    /**
     * 任务运行结束
     *
     * @param jobId   任务ID
     * @param success 任务运行是否成功
     * @param msg     任务运行结果信息
     * @return 任务运行标志
     */
    JobRunFlag runEnd(String jobId, boolean success, String msg);
}
