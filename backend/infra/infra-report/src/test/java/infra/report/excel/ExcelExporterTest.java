package infra.report.excel;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Path;
import java.time.LocalDateTime;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Excel 导出器测试
 */
class ExcelExporterTest {
    
    @TempDir
    Path tempDir;
    
    // 测试数据类
    record TestUser(Long id, String name, Integer age, LocalDateTime createTime, Boolean active) {}
    
    @Test
    void testBasicExport() throws IOException {
        // 准备测试数据
        List<TestUser> users = List.of(
                new TestUser(1L, "张三", 25, LocalDateTime.now(), true),
                new TestUser(2L, "李四", 30, LocalDateTime.now().minusDays(1), false),
                new TestUser(3L, "王五", 28, LocalDateTime.now().minusDays(2), true)
        );
        
        // 导出到文件
        Path outputFile = tempDir.resolve("test_users.xlsx");
        try (FileOutputStream fos = new FileOutputStream(outputFile.toFile())) {
            ExcelExporter.<TestUser>create()
                    .sheet("用户测试")
                    .columns(cols -> cols
                            .add("ID", TestUser::id)
                            .add("姓名", TestUser::name)
                            .add("年龄", TestUser::age, ExcelFormatter.integer())
                            .add("状态", TestUser::active, ExcelFormatter.bool())
                            .add("创建时间", TestUser::createTime, ExcelFormatter.dateTime()))
                    .dataSource(DataSource.fromCollection(users))
                    .export(fos);
        }
        
        // 验证文件存在且不为空
        assertTrue(outputFile.toFile().exists());
        assertTrue(outputFile.toFile().length() > 0);
    }
    
    @Test
    void testColumnBuilder() {
        ColumnBuilder<TestUser> builder = new ColumnBuilder<>();
        
        builder.add("ID", TestUser::id)
               .add("姓名", TestUser::name)
               .add("年龄", TestUser::age, ExcelFormatter.integer());
        
        List<ColumnConfig<TestUser>> columns = builder.build();
        assertEquals(3, columns.size());
        assertEquals("ID", columns.get(0).header());
        assertEquals("姓名", columns.get(1).header());
        assertEquals("年龄", columns.get(2).header());
    }
    
    @Test
    void testDataSourceFromCollection() {
        List<String> data = List.of("A", "B", "C");
        DataSource<String> dataSource = DataSource.fromCollection(data);
        
        List<String> result = new java.util.ArrayList<>();
        dataSource.forEach(result::add);
        
        assertEquals(data, result);
    }
    
    @Test
    void testDataSourceFilter() {
        List<Integer> numbers = List.of(1, 2, 3, 4, 5);
        DataSource<Integer> dataSource = DataSource.fromCollection(numbers)
                .filter(n -> n % 2 == 0);  // 只保留偶数
        
        List<Integer> result = new java.util.ArrayList<>();
        dataSource.forEach(result::add);
        
        assertEquals(List.of(2, 4), result);
    }
    
    @Test
    void testDataSourceMap() {
        List<String> words = List.of("hello", "world");
        DataSource<Integer> dataSource = DataSource.fromCollection(words)
                .map(String::length);  // 转换为长度
        
        List<Integer> result = new java.util.ArrayList<>();
        dataSource.forEach(result::add);
        
        assertEquals(List.of(5, 5), result);
    }
    
    @Test
    void testDataSourceLimit() {
        List<Integer> numbers = List.of(1, 2, 3, 4, 5);
        DataSource<Integer> dataSource = DataSource.fromCollection(numbers)
                .limit(3);
        
        List<Integer> result = new java.util.ArrayList<>();
        dataSource.forEach(result::add);
        
        assertEquals(List.of(1, 2, 3), result);
    }
    
    @Test
    void testFormatters() {
        // 测试日期时间格式化器
        LocalDateTime now = LocalDateTime.of(2024, 1, 1, 12, 30, 45);
        ExcelFormatter<LocalDateTime> dateTimeFormatter = ExcelFormatter.dateTime();
        assertEquals("2024-01-01 12:30:45", dateTimeFormatter.apply(now));
        
        // 测试数字格式化器
        ExcelFormatter<Number> decimalFormatter = ExcelFormatter.decimal(2);
        assertEquals(java.math.BigDecimal.valueOf(123.46), decimalFormatter.apply(123.456));
        
        // 测试布尔格式化器
        ExcelFormatter<Boolean> boolFormatter = ExcelFormatter.bool();
        assertEquals("是", boolFormatter.apply(true));
        assertEquals("否", boolFormatter.apply(false));
        
        // 测试自定义布尔格式化器
        ExcelFormatter<Boolean> customBoolFormatter = ExcelFormatter.bool("启用", "禁用");
        assertEquals("启用", customBoolFormatter.apply(true));
        assertEquals("禁用", customBoolFormatter.apply(false));
    }
    
    @Test
    void testQuickExport() throws IOException {
        Path outputFile = tempDir.resolve("quick_export.xlsx");
        
        try (FileOutputStream fos = new FileOutputStream(outputFile.toFile())) {
            List<String> data = List.of("项目1", "项目2", "项目3");
            
            ExcelExporter.<String>create()
                    .columns(cols -> cols.add("项目名称", String::toString))
                    .dataSource(DataSource.fromCollection(data))
                    .export(fos);
        }
        
        assertTrue(outputFile.toFile().exists());
        assertTrue(outputFile.toFile().length() > 0);
    }
    
    @Test
    void testEmptyDataSource() throws IOException {
        Path outputFile = tempDir.resolve("empty_export.xlsx");
        
        try (FileOutputStream fos = new FileOutputStream(outputFile.toFile())) {
            ExcelExporter.<String>create()
                    .columns(cols -> cols.add("数据", String::toString))
                    .dataSource(DataSource.empty())
                    .export(fos);
        }
        
        assertTrue(outputFile.toFile().exists());
        assertTrue(outputFile.toFile().length() > 0);  // 即使没有数据，也会有表头
    }
}
