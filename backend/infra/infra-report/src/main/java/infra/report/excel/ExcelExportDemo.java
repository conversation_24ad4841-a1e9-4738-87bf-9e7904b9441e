package infra.report.excel;

import jakarta.servlet.http.HttpServletResponse;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Excel 导出演示
 * 展示新的现代化 API 使用方法
 */
public class ExcelExportDemo {
    
    // 示例数据类
    public record User(Long id, String name, Integer age, String email, Boolean active, LocalDateTime createTime) {}
    
    /**
     * 演示基本用法
     */
    public static void basicUsage(HttpServletResponse response) throws IOException {
        // 准备测试数据
        List<User> users = List.of(
                new User(1L, "张三", 25, "<EMAIL>", true, LocalDateTime.now()),
                new User(2L, "李四", 30, "<EMAIL>", false, LocalDateTime.now().minusDays(1)),
                new User(3L, "王五", 28, "<EMAIL>", true, LocalDateTime.now().minusDays(2))
        );
        
        // 使用新的现代化 API 导出
        ExcelExporter.<User>create()
                .sheet("用户列表")
                .columns(cols -> cols
                        .add("ID", User::id)
                        .add("姓名", User::name, 20)  // 设置列宽
                        .add("年龄", User::age, ExcelFormatter.integer())
                        .add("邮箱", User::email, 30)
                        .add("状态", User::active, ExcelFormatter.bool("启用", "禁用"))
                        .add("创建时间", User::createTime, ExcelFormatter.dateTime()))
                .dataSource(DataSource.fromCollection(users))
                .batchSize(500)  // 设置批量大小
                .export(response, "用户列表");
    }
    
    /**
     * 演示条件导出
     */
    public static void conditionalExport(HttpServletResponse response, boolean includeEmail) throws IOException {
        List<User> users = List.of(
                new User(1L, "张三", 25, "<EMAIL>", true, LocalDateTime.now()),
                new User(2L, "李四", 30, "<EMAIL>", false, LocalDateTime.now().minusDays(1))
        );
        
        ExcelExporter.<User>create()
                .sheet("条件导出")
                .columns(cols -> cols
                        .add("姓名", User::name)
                        .add("年龄", User::age, ExcelFormatter.integer())
                        .addIf(includeEmail, "邮箱", User::email)  // 条件添加列
                        .add("状态", User::active, ExcelFormatter.bool()))
                .dataSource(DataSource.fromCollection(users))
                .export(response, "条件导出");
    }
    
    /**
     * 演示数据过滤和转换
     */
    public static void filterAndTransform(HttpServletResponse response) throws IOException {
        List<User> users = List.of(
                new User(1L, "张三", 17, "<EMAIL>", true, LocalDateTime.now()),
                new User(2L, "李四", 25, "<EMAIL>", true, LocalDateTime.now()),
                new User(3L, "王五", 30, "<EMAIL>", false, LocalDateTime.now())
        );
        
        ExcelExporter.<User>create()
                .sheet("成年用户")
                .columns(cols -> cols
                        .add("姓名", User::name)
                        .add("年龄", User::age, ExcelFormatter.integer())
                        .add("年龄段", user -> {
                            int age = user.age();
                            return switch (age / 10) {
                                case 1 -> "青少年";
                                case 2 -> "青年";
                                case 3 -> "中年";
                                default -> "其他";
                            };
                        }))
                .dataSource(DataSource.fromCollection(users)
                        .filter(user -> user.age() >= 18)  // 只导出成年用户
                        .filter(User::active))             // 只导出活跃用户
                .export(response, "成年活跃用户");
    }
    
    /**
     * 演示快速导出
     */
    public static void quickExport(HttpServletResponse response) throws IOException {
        // 单列快速导出
        List<String> names = List.of("张三", "李四", "王五");
        ExcelExporter.quickExport(response, "姓名列表", "姓名", names);
    }
    
    /**
     * 演示多列快速导出
     */
    public static void quickMultiColumnExport(HttpServletResponse response) throws IOException {
        List<String> headers = List.of("姓名", "年龄", "城市");
        List<List<Object>> data = List.of(
                List.of("张三", 25, "北京"),
                List.of("李四", 30, "上海"),
                List.of("王五", 28, "广州")
        );
        ExcelExporter.quickExport(response, "用户信息", headers, data);
    }
    
    /**
     * 演示自定义格式化
     */
    public static void customFormatting(HttpServletResponse response) throws IOException {
        List<User> users = List.of(
                new User(1L, "张三", 25, "<EMAIL>", true, LocalDateTime.now()),
                new User(2L, "李四", 30, "<EMAIL>", false, LocalDateTime.now())
        );
        
        ExcelExporter.<User>create()
                .sheet("自定义格式")
                .columns(cols -> cols
                        .add("用户信息", user -> user.name() + "(" + user.age() + "岁)")
                        .add("邮箱域名", user -> {
                            String email = user.email();
                            return email.substring(email.indexOf("@") + 1);
                        })
                        .add("账龄(天)", user -> 
                                java.time.Duration.between(user.createTime(), LocalDateTime.now()).toDays(),
                                ExcelFormatter.integer())
                        .add("状态图标", user -> user.active() ? "✓" : "✗"))
                .dataSource(DataSource.fromCollection(users))
                .export(response, "自定义格式");
    }
    
    /**
     * 演示使用传统 API（向后兼容）
     */
    public static void legacyApiExample(HttpServletResponse response) throws IOException {
        List<User> users = List.of(
                new User(1L, "张三", 25, "<EMAIL>", true, LocalDateTime.now())
        );
        
        // 使用传统的快速导出方法
        ExcelExport.quickExport(response, "传统API", "姓名", 
                users.stream().map(User::name).toList());
    }
}
