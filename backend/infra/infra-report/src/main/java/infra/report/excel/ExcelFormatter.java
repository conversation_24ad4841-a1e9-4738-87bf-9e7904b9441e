package infra.report.excel;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.function.Function;

/**
 * Excel 格式化器
 * 提供常用的数据格式化功能
 */
public interface ExcelFormatter<T> extends Function<T, Object> {
    
    /**
     * 原始值格式化器（不做任何转换）
     */
    static <T> ExcelFormatter<T> raw() {
        return value -> value;
    }
    
    /**
     * 字符串格式化器
     */
    static <T> ExcelFormatter<T> string() {
        return value -> value == null ? "" : value.toString();
    }
    
    /**
     * 日期时间格式化器
     */
    static ExcelFormatter<LocalDateTime> dateTime() {
        return dateTime("yyyy-MM-dd HH:mm:ss");
    }
    
    /**
     * 自定义日期时间格式化器
     */
    static ExcelFormatter<LocalDateTime> dateTime(String pattern) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
        return value -> value == null ? "" : value.format(formatter);
    }
    
    /**
     * 日期格式化器
     */
    static ExcelFormatter<LocalDate> date() {
        return date("yyyy-MM-dd");
    }
    
    /**
     * 自定义日期格式化器
     */
    static ExcelFormatter<LocalDate> date(String pattern) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
        return value -> value == null ? "" : value.format(formatter);
    }
    
    /**
     * 数字格式化器（保留2位小数）
     */
    static ExcelFormatter<Number> decimal() {
        return decimal(2);
    }
    
    /**
     * 数字格式化器（指定小数位数）
     */
    static ExcelFormatter<Number> decimal(int scale) {
        return value -> {
            if (value == null) return "";
            return BigDecimal.valueOf(value.doubleValue())
                    .setScale(scale, RoundingMode.HALF_UP);
        };
    }
    
    /**
     * 整数格式化器
     */
    static ExcelFormatter<Number> integer() {
        return value -> value == null ? "" : value.longValue();
    }
    
    /**
     * 布尔值格式化器
     */
    static ExcelFormatter<Boolean> bool() {
        return bool("是", "否");
    }
    
    /**
     * 自定义布尔值格式化器
     */
    static ExcelFormatter<Boolean> bool(String trueText, String falseText) {
        return value -> {
            if (value == null) return "";
            return value ? trueText : falseText;
        };
    }
    
    /**
     * 枚举格式化器（使用 name()）
     */
    static <E extends Enum<E>> ExcelFormatter<E> enumName() {
        return value -> value == null ? "" : value.name();
    }
    
    /**
     * 枚举格式化器（使用 toString()）
     */
    static <E extends Enum<E>> ExcelFormatter<E> enumString() {
        return value -> value == null ? "" : value.toString();
    }
    
    /**
     * 自定义格式化器
     */
    static <T> ExcelFormatter<T> custom(Function<T, Object> formatter) {
        return formatter::apply;
    }
    
    /**
     * 组合格式化器（先应用第一个，再应用第二个）
     */
    default <R> ExcelFormatter<T> then(Function<Object, R> after) {
        return value -> after.apply(this.apply(value));
    }
}
