package infra.report.excel.importer;

import cn.idev.excel.ExcelReader;
import cn.idev.excel.FastExcel;
import cn.idev.excel.read.listener.ReadListener;
import cn.idev.excel.read.metadata.ReadSheet;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;
import java.util.function.Supplier;

/**
 * Excel 导入器
 * 提供流式、内存高效的 Excel 导入功能
 *
 * <h3>使用示例：</h3>
 * <pre>{@code
 * // 基本导入
 * ImportResult<User> result = ExcelImporter.create(User::new)
 *     .columns(cols -> cols
 *         .required("姓名", ExcelParser.string(), User::setName)
 *         .add("年龄", ExcelParser.integer(), User::setAge)
 *         .add("邮箱", ExcelParser.string(), User::setEmail))
 *     .importFrom(file);
 *
 * // 容错导入
 * ImportResult<User> result = ExcelImporter.create(User::new)
 *     .columns(cols -> cols
 *         .add("ID", ExcelParser.longValue().optional(), User::setId)
 *         .required("姓名", ExcelParser.string(), User::setName)
 *         .withDefault("年龄", ExcelParser.integer().orDefault(0), User::setAge, 18))
 *     .skipEmptyRows(true)
 *     .headerRow(1)
 *     .importFrom(file);
 * }</pre>
 */
public class ExcelImporter<T> {
    private Supplier<T> objectFactory;
    private List<ColumnConfig<T>> columns = new ArrayList<>();
    private int headerRowIndex = 0;
    private boolean skipEmptyRows = true;
    private String sheetName = null;
    private int sheetIndex = 0;
    private int maxRows = -1;

    private ExcelImporter() {}

    /**
     * 创建导入器
     *
     * @param objectFactory 对象工厂，用于创建目标对象实例
     * @return Excel 导入器实例
     */
    public static <T> ExcelImporter<T> create(Supplier<T> objectFactory) {
        ExcelImporter<T> importer = new ExcelImporter<>();
        importer.objectFactory = objectFactory;
        return importer;
    }

    /**
     * 配置列
     *
     * @param columnConfig 列配置回调
     * @return 当前导入器实例
     */
    public ExcelImporter<T> columns(Consumer<ColumnBuilder<T>> columnConfig) {
        ColumnBuilder<T> builder = new ColumnBuilder<>();
        columnConfig.accept(builder);
        this.columns = builder.build();
        return this;
    }

    /**
     * 设置表头行索引（从0开始）
     *
     * @param headerRowIndex 表头行索引
     * @return 当前导入器实例
     */
    public ExcelImporter<T> headerRow(int headerRowIndex) {
        this.headerRowIndex = headerRowIndex;
        return this;
    }

    /**
     * 设置是否跳过空行
     *
     * @param skipEmptyRows 是否跳过空行
     * @return 当前导入器实例
     */
    public ExcelImporter<T> skipEmptyRows(boolean skipEmptyRows) {
        this.skipEmptyRows = skipEmptyRows;
        return this;
    }

    /**
     * 设置要读取的工作表名称
     *
     * @param sheetName 工作表名称
     * @return 当前导入器实例
     */
    public ExcelImporter<T> sheet(String sheetName) {
        this.sheetName = sheetName;
        return this;
    }

    /**
     * 设置要读取的工作表索引（从0开始）
     *
     * @param sheetIndex 工作表索引
     * @return 当前导入器实例
     */
    public ExcelImporter<T> sheet(int sheetIndex) {
        this.sheetIndex = sheetIndex;
        this.sheetName = null;
        return this;
    }

    /**
     * 设置最大读取行数（不包括表头）
     *
     * @param maxRows 最大行数，-1表示不限制
     * @return 当前导入器实例
     */
    public ExcelImporter<T> maxRows(int maxRows) {
        this.maxRows = maxRows;
        return this;
    }

    /**
     * 从文件导入
     *
     * @param file 上传的文件
     * @return 导入结果
     * @throws IOException IO异常
     */
    public ImportResult<T> importFrom(MultipartFile file) throws IOException {
        try (InputStream inputStream = file.getInputStream()) {
            return importFrom(inputStream);
        }
    }

    /**
     * 从输入流导入
     *
     * @param inputStream 输入流
     * @return 导入结果
     */
    public ImportResult<T> importFrom(InputStream inputStream) {
        validateConfiguration();

        ImportResult.Builder<T> resultBuilder = new ImportResult.Builder<>();
        ImportReadListener listener = new ImportReadListener(resultBuilder);

        try (ExcelReader reader = FastExcel.read(inputStream, listener).build()) {
            ReadSheet readSheet = createReadSheet();
            reader.read(readSheet);
        }

        return resultBuilder.build();
    }

    /**
     * 流式导入（适合大文件）
     *
     * @param inputStream 输入流
     * @param batchProcessor 批处理器，每批数据的处理逻辑
     * @param batchSize 批大小
     */
    public void importStream(InputStream inputStream, Consumer<List<T>> batchProcessor, int batchSize) {
        validateConfiguration();

        if (batchSize <= 0) {
            throw new IllegalArgumentException("批大小必须大于0");
        }

        StreamImportListener listener = new StreamImportListener(batchProcessor, batchSize);

        try (ExcelReader reader = FastExcel.read(inputStream, listener).build()) {
            ReadSheet readSheet = createReadSheet();
            reader.read(readSheet);
        }

        // 处理最后一批数据
        listener.flushBatch();
    }

    /**
     * 流式导入（使用默认批大小1000）
     *
     * @param inputStream 输入流
     * @param batchProcessor 批处理器
     */
    public void importStream(InputStream inputStream, Consumer<List<T>> batchProcessor) {
        importStream(inputStream, batchProcessor, 1000);
    }

    /**
     * 流式导入（从文件）
     *
     * @param file 上传的文件
     * @param batchProcessor 批处理器
     * @param batchSize 批大小
     * @throws IOException IO异常
     */
    public void importStream(MultipartFile file, Consumer<List<T>> batchProcessor, int batchSize) throws IOException {
        try (InputStream inputStream = file.getInputStream()) {
            importStream(inputStream, batchProcessor, batchSize);
        }
    }

    /**
     * 流式导入（从文件，使用默认批大小）
     *
     * @param file 上传的文件
     * @param batchProcessor 批处理器
     * @throws IOException IO异常
     */
    public void importStream(MultipartFile file, Consumer<List<T>> batchProcessor) throws IOException {
        importStream(file, batchProcessor, 1000);
    }

    /**
     * 重置配置
     *
     * @return 当前导入器实例
     */
    public ExcelImporter<T> reset() {
        this.columns.clear();
        this.headerRowIndex = 0;
        this.skipEmptyRows = true;
        this.sheetName = null;
        this.sheetIndex = 0;
        this.maxRows = -1;
        return this;
    }

    /**
     * 复制当前配置创建新导入器
     *
     * @param newObjectFactory 新的对象工厂
     * @return 新导入器实例
     */
    public <R> ExcelImporter<R> copyWith(Supplier<R> newObjectFactory) {
        ExcelImporter<R> newImporter = ExcelImporter.create(newObjectFactory);
        newImporter.headerRowIndex = this.headerRowIndex;
        newImporter.skipEmptyRows = this.skipEmptyRows;
        newImporter.sheetName = this.sheetName;
        newImporter.sheetIndex = this.sheetIndex;
        newImporter.maxRows = this.maxRows;
        // 注意：列配置不能直接复制，因为泛型类型不同
        return newImporter;
    }

    /**
     * 获取列配置信息
     *
     * @return 列配置列表的只读副本
     */
    public List<ColumnConfig<T>> getColumnConfigs() {
        return new ArrayList<>(columns);
    }

    /**
     * 获取表头列表
     *
     * @return 表头列表
     */
    public List<String> getHeaders() {
        return columns.stream()
                .map(ColumnConfig::header)
                .toList();
    }

    /**
     * 获取必填列表头
     *
     * @return 必填列表头列表
     */
    public List<String> getRequiredHeaders() {
        return columns.stream()
                .filter(ColumnConfig::required)
                .map(ColumnConfig::header)
                .toList();
    }

    /**
     * 检查是否配置了指定表头的列
     *
     * @param header 表头名称
     * @return 是否存在该列配置
     */
    public boolean hasColumn(String header) {
        return columns.stream()
                .anyMatch(col -> col.header().equals(header));
    }

    /**
     * 获取配置摘要信息
     *
     * @return 配置摘要
     */
    public String getConfigSummary() {
        StringBuilder sb = new StringBuilder();
        sb.append("Excel导入器配置摘要:\n");
        sb.append("- 表头行索引: ").append(headerRowIndex).append("\n");
        sb.append("- 跳过空行: ").append(skipEmptyRows).append("\n");
        sb.append("- 工作表: ").append(sheetName != null ? sheetName : "索引" + sheetIndex).append("\n");
        sb.append("- 最大行数: ").append(maxRows > 0 ? maxRows : "无限制").append("\n");
        sb.append("- 配置列数: ").append(columns.size()).append("\n");

        if (!columns.isEmpty()) {
            sb.append("- 列配置:\n");
            for (ColumnConfig<T> column : columns) {
                sb.append("  * ").append(column.header());
                if (column.required()) {
                    sb.append(" (必填)");
                }
                if (column.defaultValue() != null) {
                    sb.append(" [默认值: ").append(column.defaultValue()).append("]");
                }
                sb.append("\n");
            }
        }

        return sb.toString();
    }

    /**
     * 创建读取工作表配置
     */
    private ReadSheet createReadSheet() {
        ReadSheet readSheet = new ReadSheet();

        if (sheetName != null) {
            readSheet.setSheetName(sheetName);
        } else {
            readSheet.setSheetNo(sheetIndex);
        }

        readSheet.setHeadRowNumber(headerRowIndex + 1);

        return readSheet;
    }

    /**
     * 验证配置
     */
    private void validateConfiguration() {
        if (objectFactory == null) {
            throw new IllegalStateException("未设置对象工厂");
        }
        if (columns.isEmpty()) {
            throw new IllegalStateException("未配置任何列");
        }
        if (headerRowIndex < 0) {
            throw new IllegalArgumentException("表头行索引不能为负数");
        }
    }

    /**
     * 导入读取监听器
     */
    private class ImportReadListener implements ReadListener<Map<Integer, String>> {
        private final ImportResult.Builder<T> resultBuilder;
        private Map<String, Integer> headerMap;
        private int currentRowIndex = 0;
        private int processedRows = 0;

        public ImportReadListener(ImportResult.Builder<T> resultBuilder) {
            this.resultBuilder = resultBuilder;
        }

        @Override
        public void invoke(Map<Integer, String> data, cn.idev.excel.context.AnalysisContext context) {
            try {
                // 检查最大行数限制
                if (maxRows > 0 && processedRows >= maxRows) {
                    return;
                }

                // 第一次调用时构建表头映射
                if (headerMap == null) {
                    buildHeaderMap(data);
                    return;
                }

                T obj = parseRow(data, currentRowIndex);
                if (obj != null) {
                    resultBuilder.addSuccess(obj);
                    processedRows++;
                }
            } catch (Exception e) {
                resultBuilder.addError(currentRowIndex, e.getMessage(), e);
            }
            currentRowIndex++;
        }

        @Override
        public void doAfterAllAnalysed(cn.idev.excel.context.AnalysisContext context) {
            // 读取完成，可以在这里做一些清理工作
        }

        /**
         * 构建表头映射
         */
        private void buildHeaderMap(Map<Integer, String> headerRow) {
            headerMap = new HashMap<>();
            for (Map.Entry<Integer, String> entry : headerRow.entrySet()) {
                String header = entry.getValue();
                if (header != null && !header.trim().isEmpty()) {
                    headerMap.put(header.trim(), entry.getKey());
                }
            }

            // 验证必填列是否存在
            validateRequiredHeaders();
        }

        /**
         * 验证必填列是否存在
         */
        private void validateRequiredHeaders() {
            List<String> missingHeaders = new ArrayList<>();
            for (ColumnConfig<T> column : columns) {
                if (column.required() && !headerMap.containsKey(column.header())) {
                    missingHeaders.add(column.header());
                }
            }

            if (!missingHeaders.isEmpty()) {
                throw new IllegalArgumentException(
                        "Excel文件缺少必需的列: " + String.join(", ", missingHeaders));
            }
        }

        /**
         * 解析行数据
         */
        private T parseRow(Map<Integer, String> rowData, int rowIndex) {
            if (skipEmptyRows && isEmptyRow(rowData)) {
                return null;
            }

            T obj = objectFactory.get();

            for (ColumnConfig<T> column : columns) {
                Integer columnIndex = headerMap.get(column.header());
                if (columnIndex != null) {
                    String cellValue = rowData.get(columnIndex);
                    column.parseAndSet(obj, cellValue, rowIndex);
                } else if (column.required()) {
                    throw new ExcelParser.ExcelParseException(
                            String.format("第%d行，找不到必需的列: %s", rowIndex + 1, column.header()));
                }
            }

            return obj;
        }

        /**
         * 判断是否为空行
         */
        private boolean isEmptyRow(Map<Integer, String> rowData) {
            return rowData.values().stream()
                    .allMatch(value -> value == null || value.trim().isEmpty());
        }
    }

    /**
     * 流式导入监听器
     */
    private class StreamImportListener implements ReadListener<Map<Integer, String>> {
        private final Consumer<List<T>> batchProcessor;
        private final int batchSize;
        private final List<T> currentBatch = new ArrayList<>();
        private Map<String, Integer> headerMap;
        private int currentRowIndex = 0;
        private int processedRows = 0;

        public StreamImportListener(Consumer<List<T>> batchProcessor, int batchSize) {
            this.batchProcessor = batchProcessor;
            this.batchSize = batchSize;
        }

        @Override
        public void invoke(Map<Integer, String> data, cn.idev.excel.context.AnalysisContext context) {
            try {
                // 检查最大行数限制
                if (maxRows > 0 && processedRows >= maxRows) {
                    return;
                }

                // 第一次调用时构建表头映射
                if (headerMap == null) {
                    buildHeaderMap(data);
                    return;
                }

                T obj = parseRow(data, currentRowIndex);
                if (obj != null) {
                    currentBatch.add(obj);
                    processedRows++;

                    // 达到批大小时处理批数据
                    if (currentBatch.size() >= batchSize) {
                        processBatch();
                    }
                }
            } catch (Exception e) {
                // 流式处理中的错误可以记录日志，但不中断处理
                System.err.println("第" + (currentRowIndex + 1) + "行处理失败: " + e.getMessage());
            }
            currentRowIndex++;
        }

        @Override
        public void doAfterAllAnalysed(cn.idev.excel.context.AnalysisContext context) {
            // 处理最后一批数据
            flushBatch();
        }

        /**
         * 处理当前批数据
         */
        private void processBatch() {
            if (!currentBatch.isEmpty()) {
                batchProcessor.accept(new ArrayList<>(currentBatch));
                currentBatch.clear();
            }
        }

        /**
         * 刷新剩余批数据
         */
        public void flushBatch() {
            processBatch();
        }

        /**
         * 构建表头映射
         */
        private void buildHeaderMap(Map<Integer, String> headerRow) {
            headerMap = new HashMap<>();
            for (Map.Entry<Integer, String> entry : headerRow.entrySet()) {
                String header = entry.getValue();
                if (header != null && !header.trim().isEmpty()) {
                    headerMap.put(header.trim(), entry.getKey());
                }
            }

            // 验证必填列是否存在
            validateRequiredHeaders();
        }

        /**
         * 验证必填列是否存在
         */
        private void validateRequiredHeaders() {
            List<String> missingHeaders = new ArrayList<>();
            for (ColumnConfig<T> column : columns) {
                if (column.required() && !headerMap.containsKey(column.header())) {
                    missingHeaders.add(column.header());
                }
            }

            if (!missingHeaders.isEmpty()) {
                throw new IllegalArgumentException(
                        "Excel文件缺少必需的列: " + String.join(", ", missingHeaders));
            }
        }

        /**
         * 解析行数据
         */
        private T parseRow(Map<Integer, String> rowData, int rowIndex) {
            if (skipEmptyRows && isEmptyRow(rowData)) {
                return null;
            }

            T obj = objectFactory.get();

            for (ColumnConfig<T> column : columns) {
                Integer columnIndex = headerMap.get(column.header());
                if (columnIndex != null) {
                    String cellValue = rowData.get(columnIndex);
                    column.parseAndSet(obj, cellValue, rowIndex);
                } else if (column.required()) {
                    throw new ExcelParser.ExcelParseException(
                            String.format("第%d行，找不到必需的列: %s", rowIndex + 1, column.header()));
                }
            }

            return obj;
        }

        /**
         * 判断是否为空行
         */
        private boolean isEmptyRow(Map<Integer, String> rowData) {
            return rowData.values().stream()
                    .allMatch(value -> value == null || value.trim().isEmpty());
        }
    }
}
