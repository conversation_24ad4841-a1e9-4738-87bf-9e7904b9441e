package infra.report.excel.importer;

import java.util.ArrayList;
import java.util.List;
import java.util.function.BiConsumer;

/**
 * 导入列配置构建器
 * 提供流式 API 来配置 Excel 导入列
 */
public class ImportColumnBuilder<T> {
    private final List<ImportColumnConfig<T>> columns = new ArrayList<>();

    /**
     * 添加简单列
     */
    public ImportColumnBuilder<T> add(String header, BiConsumer<T, String> setter) {
        columns.add(ImportColumnConfig.of(header, ExcelParser.string(), setter));
        return this;
    }
    
    /**
     * 添加带解析器的列
     */
    public <R> ImportColumnBuilder<T> add(String header, ExcelParser<R> parser, BiConsumer<T, R> setter) {
        columns.add(ImportColumnConfig.of(header, parser, setter));
        return this;
    }
    
    /**
     * 添加必填列
     */
    public <R> ImportColumnBuilder<T> required(String header, ExcelParser<R> parser, BiConsumer<T, R> setter) {
        columns.add(ImportColumnConfig.required(header, parser, setter));
        return this;
    }
    
    /**
     * 添加带默认值的列
     */
    public <R> ImportColumnBuilder<T> withDefault(String header, ExcelParser<R> parser, 
                                                 BiConsumer<T, R> setter, R defaultValue) {
        columns.add(ImportColumnConfig.withDefault(header, parser, setter, defaultValue));
        return this;
    }
    
    /**
     * 条件添加列
     */
    public ImportColumnBuilder<T> addIf(boolean condition, String header, BiConsumer<T, String> setter) {
        if (condition) {
            add(header, setter);
        }
        return this;
    }
    
    /**
     * 条件添加带解析器的列
     */
    public <R> ImportColumnBuilder<T> addIf(boolean condition, String header, 
                                           ExcelParser<R> parser, BiConsumer<T, R> setter) {
        if (condition) {
            add(header, parser, setter);
        }
        return this;
    }
    
    /**
     * 添加列配置
     */
    public ImportColumnBuilder<T> add(ImportColumnConfig<T> column) {
        columns.add(column);
        return this;
    }
    
    /**
     * 获取所有列配置
     */
    public List<ImportColumnConfig<T>> build() {
        return new ArrayList<>(columns);
    }
    
    /**
     * 获取表头列表
     */
    public List<String> getHeaders() {
        return columns.stream()
                .map(ImportColumnConfig::header)
                .toList();
    }
    
    /**
     * 清空所有列
     */
    public ImportColumnBuilder<T> clear() {
        columns.clear();
        return this;
    }
    
    /**
     * 获取列数量
     */
    public int size() {
        return columns.size();
    }
    
    /**
     * 是否为空
     */
    public boolean isEmpty() {
        return columns.isEmpty();
    }
}
