package infra.report.excel.export;

import cn.idev.excel.ExcelWriter;
import cn.idev.excel.FastExcel;
import cn.idev.excel.write.metadata.WriteSheet;
import infra.core.web.ServletUtil;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;

import java.io.IOException;
import java.io.OutputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Consumer;

/**
 * Excel 导出器
 * 提供流式、内存高效的 Excel 导出功能
 */
public class ExcelExporter<T> {
    private String sheetName = "Sheet1";
    private List<ColumnConfig<T>> columns = new ArrayList<>();
    private DataSource<T> dataSource = DataSource.empty();
    private int batchSize = 1000; // 批量写入大小
    
    private ExcelExporter() {}
    
    /**
     * 创建导出器
     */
    public static <T> ExcelExporter<T> create() {
        return new ExcelExporter<>();
    }
    
    /**
     * 设置工作表名称
     */
    public ExcelExporter<T> sheet(String sheetName) {
        this.sheetName = sheetName;
        return this;
    }
    
    /**
     * 配置列
     */
    public ExcelExporter<T> columns(Consumer<ColumnBuilder<T>> columnConfig) {
        ColumnBuilder<T> builder = new ColumnBuilder<>();
        columnConfig.accept(builder);
        this.columns = builder.build();
        return this;
    }
    
    /**
     * 直接设置列配置
     */
    public ExcelExporter<T> columns(List<ColumnConfig<T>> columns) {
        this.columns = new ArrayList<>(columns);
        return this;
    }
    
    /**
     * 设置数据源
     */
    public ExcelExporter<T> dataSource(DataSource<T> dataSource) {
        this.dataSource = dataSource;
        return this;
    }
    
    /**
     * 设置批量写入大小
     */
    public ExcelExporter<T> batchSize(int batchSize) {
        this.batchSize = Math.max(1, batchSize);
        return this;
    }
    
    /**
     * 导出到 HTTP 响应
     */
    public void export(HttpServletResponse response, String fileNamePrefix) throws IOException {
        String fileName = generateFileName(fileNamePrefix);
        try (ServletOutputStream outputStream = ServletUtil.writeFile(response, fileName, 
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")) {
            export(outputStream);
        }
    }
    
    /**
     * 导出到输出流
     */
    public void export(OutputStream outputStream) {
        if (columns.isEmpty()) {
            throw new IllegalStateException("未配置任何列");
        }
        
        // 准备表头
        List<List<String>> headers = columns.stream()
                .map(col -> List.of(col.header()))
                .toList();
        
        try (ExcelWriter writer = FastExcel.write(outputStream).head(headers).build()) {
            WriteSheet writeSheet = new WriteSheet();
            writeSheet.setSheetName(sheetName);
            
            // 设置列宽
            setColumnWidths(writeSheet);
            
            // 批量写入数据
            writeBatchData(writer, writeSheet);
        }
    }
    
    /**
     * 设置列宽
     */
    private void setColumnWidths(WriteSheet writeSheet) {
        for (ColumnConfig<T> column : columns) {
            if (column.width() != null) {
                // FastExcel 的列宽设置方式可能需要根据具体版本调整，这里提供一个基本实现框架
            }
        }
    }
    
    /**
     * 批量写入数据
     */
    private void writeBatchData(ExcelWriter writer, WriteSheet writeSheet) {
        List<List<Object>> batch = new ArrayList<>(batchSize);
        
        dataSource.forEach(item -> {
            // 提取并格式化行数据
            List<Object> rowData = columns.stream()
                    .map(col -> col.extractAndFormat(item))
                    .toList();
            
            batch.add(rowData);
            
            // 达到批量大小时写入
            if (batch.size() >= batchSize) {
                writer.write(new ArrayList<>(batch), writeSheet);
                batch.clear();
            }
        });
        
        // 写入剩余数据
        if (!batch.isEmpty()) {
            writer.write(batch, writeSheet);
        }
    }
    
    /**
     * 生成文件名
     */
    private String generateFileName(String prefix) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        return prefix + "_" + timestamp + ".xlsx";
    }
    
    // ==================== 便捷方法 ====================
    
    /**
     * 快速导出（单列）
     */
    public static <T> void quickExport(HttpServletResponse response, String fileName, 
                                     String header, List<T> data) throws IOException {
        ExcelExporter.<T>create()
                .columns(cols -> cols.add(header, Object::toString))
                .dataSource(DataSource.fromCollection(data))
                .export(response, fileName);
    }
    
    /**
     * 快速导出（多列，使用字符串转换）
     */
    public static void quickExport(HttpServletResponse response, String fileName,
                                 List<String> headers, List<List<Object>> data) throws IOException {
        if (headers.isEmpty() || data.isEmpty()) {
            throw new IllegalArgumentException("表头和数据不能为空");
        }
        
        ExcelExporter.<List<Object>>create()
                .columns(cols -> {
                    for (int i = 0; i < headers.size(); i++) {
                        final int index = i;
                        cols.add(headers.get(i), row -> 
                                index < row.size() ? row.get(index) : "");
                    }
                })
                .dataSource(DataSource.fromCollection(data))
                .export(response, fileName);
    }
}
