package infra.report.excel.exporter;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;

/**
 * 列配置构建器
 * 提供流式 API 来配置 Excel 列
 */
public class ColumnBuilder<T> {
    private final List<ColumnConfig<T>> columns = new ArrayList<>();
    
    /**
     * 添加简单列
     */
    public ColumnBuilder<T> add(String header, Function<T, ?> extractor) {
        columns.add(ColumnConfig.of(header, extractor));
        return this;
    }
    
    /**
     * 添加带格式化器的列
     */
    public <R> ColumnBuilder<T> add(String header, Function<T, R> extractor, ExcelFormatter<R> formatter) {
        columns.add(ColumnConfig.of(header, extractor, formatter));
        return this;
    }
    
    /**
     * 添加带宽度的列
     */
    public ColumnBuilder<T> add(String header, Function<T, ?> extractor, int width) {
        columns.add(ColumnConfig.of(header, extractor, width));
        return this;
    }
    
    /**
     * 添加完整配置的列
     */
    public <R> ColumnBuilder<T> add(String header, Function<T, R> extractor, ExcelFormatter<R> formatter, int width) {
        columns.add(ColumnConfig.of(header, extractor, formatter, width));
        return this;
    }
    
    /**
     * 添加列配置
     */
    public ColumnBuilder<T> add(ColumnConfig<T> column) {
        columns.add(column);
        return this;
    }
    
    /**
     * 条件添加列
     */
    public ColumnBuilder<T> addIf(boolean condition, String header, Function<T, ?> extractor) {
        if (condition) {
            add(header, extractor);
        }
        return this;
    }
    
    /**
     * 条件添加带格式化器的列
     */
    public <R> ColumnBuilder<T> addIf(boolean condition, String header, Function<T, R> extractor, ExcelFormatter<R> formatter) {
        if (condition) {
            add(header, extractor, formatter);
        }
        return this;
    }
    
    /**
     * 获取所有列配置
     */
    public List<ColumnConfig<T>> build() {
        return new ArrayList<>(columns);
    }
    
    /**
     * 获取表头列表
     */
    public List<List<String>> getHeaders() {
        return columns.stream()
                .map(col -> List.of(col.header()))
                .toList();
    }
    
    /**
     * 清空所有列
     */
    public ColumnBuilder<T> clear() {
        columns.clear();
        return this;
    }
    
    /**
     * 获取列数量
     */
    public int size() {
        return columns.size();
    }
    
    /**
     * 是否为空
     */
    public boolean isEmpty() {
        return columns.isEmpty();
    }
}
