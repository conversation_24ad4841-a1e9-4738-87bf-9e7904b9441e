package infra.report.excel.exporter;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import infra.domain.entity.IdEntity;
import infra.domain.service.ServiceBase;

import java.util.Collection;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Stream;

/**
 * Excel 数据源抽象
 * 支持多种数据源类型的统一处理
 */
@FunctionalInterface
public interface DataSource<T> {
    
    /**
     * 流式处理数据
     * 
     * @param processor 数据处理器
     */
    void forEach(Consumer<T> processor);
    
    /**
     * 从集合创建数据源
     */
    static <T> DataSource<T> fromCollection(Collection<T> collection) {
        return collection::forEach;
    }
    
    /**
     * 从流创建数据源
     */
    static <T> DataSource<T> fromStream(Stream<T> stream) {
        return stream::forEach;
    }
    
    /**
     * 从 ServiceBase 创建数据源
     */
    static <T extends IdEntity> DataSource<T> fromService(
            ServiceBase<?, T> service,
            Consumer<QueryWrapper<T>> queryBuilder) {
        return processor -> service.streamQuery(queryBuilder, processor);
    }
    
    /**
     * 从 ServiceBase 创建数据源（无查询条件）
     */
    static <T extends IdEntity> DataSource<T> fromService(ServiceBase<?, T> service) {
        return processor -> service.streamQuery(null, processor);
    }
    
    /**
     * 从自定义回调创建数据源
     */
    static <T> DataSource<T> fromCallback(Consumer<Consumer<T>> callback) {
        return callback::accept;
    }
    
    /**
     * 空数据源
     */
    static <T> DataSource<T> empty() {
        return processor -> {
            // 不做任何处理
        };
    }
    
    /**
     * 单个元素数据源
     */
    static <T> DataSource<T> single(T item) {
        return processor -> processor.accept(item);
    }
    
    /**
     * 过滤数据源
     */
    default DataSource<T> filter(Predicate<T> predicate) {
        return processor -> this.forEach(item -> {
            if (predicate.test(item)) {
                processor.accept(item);
            }
        });
    }
    
    /**
     * 转换数据源
     */
    default <R> DataSource<R> map(Function<T, R> mapper) {
        return processor -> this.forEach(item -> processor.accept(mapper.apply(item)));
    }
    
    /**
     * 限制数据源大小
     */
    default DataSource<T> limit(long maxSize) {
        return processor -> {
            long[] count = {0};
            this.forEach(item -> {
                if (count[0] < maxSize) {
                    processor.accept(item);
                    count[0]++;
                }
            });
        };
    }
}
