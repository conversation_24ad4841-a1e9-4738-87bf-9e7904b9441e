package infra.report.excel;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import infra.domain.entity.IdEntity;
import infra.domain.service.ServiceBase;
import jakarta.servlet.http.HttpServletResponse;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Excel 导出使用示例
 * 
 * <p>本类展示了如何使用现代化的 Excel 导出 API</p>
 */
public class ExcelExportExample {
    
    // 示例实体类
    public static class User extends IdEntity {
        private String name;
        private Integer age;
        private String email;
        private Boolean active;
        private LocalDateTime createTime;
        
        // getters and setters
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        public Integer getAge() { return age; }
        public void setAge(Integer age) { this.age = age; }
        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }
        public Boolean getActive() { return active; }
        public void setActive(Boolean active) { this.active = active; }
        public LocalDateTime getCreateTime() { return createTime; }
        public void setCreateTime(LocalDateTime createTime) { this.createTime = createTime; }
    }
    
    /**
     * 示例1：基本用法 - 从 ServiceBase 导出
     */
    public static void exportUsers(HttpServletResponse response, 
                                 ServiceBase<?, User> userService) throws IOException {
        ExcelExporter.<User>create()
                .sheet("用户列表")
                .columns(cols -> cols
                        .add("ID", User::getId)
                        .add("姓名", User::getName)
                        .add("年龄", User::getAge, ExcelFormatter.integer())
                        .add("邮箱", User::getEmail)
                        .add("状态", User::getActive, ExcelFormatter.bool("启用", "禁用"))
                        .add("创建时间", User::getCreateTime, ExcelFormatter.dateTime()))
                .dataSource(DataSource.fromService(userService, wrapper -> 
                        wrapper.eq("deleted", false).orderByDesc("create_time")))
                .export(response, "用户列表");
    }
    
    /**
     * 示例2：从集合导出
     */
    public static void exportUserList(HttpServletResponse response, 
                                    List<User> users) throws IOException {
        ExcelExporter.<User>create()
                .sheet("用户数据")
                .columns(cols -> cols
                        .add("姓名", User::getName, 20)  // 设置列宽
                        .add("年龄", User::getAge, ExcelFormatter.integer(), 10)
                        .add("邮箱", User::getEmail, 30))
                .dataSource(DataSource.fromCollection(users))
                .batchSize(500)  // 设置批量大小
                .export(response, "用户数据");
    }
    
    /**
     * 示例3：条件导出
     */
    public static void exportActiveUsers(HttpServletResponse response,
                                       ServiceBase<?, User> userService,
                                       boolean includeEmail) throws IOException {
        ExcelExporter.<User>create()
                .sheet("活跃用户")
                .columns(cols -> cols
                        .add("姓名", User::getName)
                        .add("年龄", User::getAge, ExcelFormatter.integer())
                        .addIf(includeEmail, "邮箱", User::getEmail)  // 条件添加列
                        .add("创建时间", User::getCreateTime, ExcelFormatter.dateTime()))
                .dataSource(DataSource.fromService(userService, wrapper -> 
                        wrapper.eq("active", true)))
                .export(response, "活跃用户");
    }
    
    /**
     * 示例4：自定义格式化
     */
    public static void exportUsersWithCustomFormat(HttpServletResponse response,
                                                  List<User> users) throws IOException {
        ExcelExporter.<User>create()
                .columns(cols -> cols
                        .add("用户信息", user -> user.getName() + "(" + user.getAge() + "岁)")
                        .add("邮箱域名", user -> {
                            String email = user.getEmail();
                            return email != null && email.contains("@") 
                                    ? email.substring(email.indexOf("@") + 1) 
                                    : "";
                        })
                        .add("账龄(天)", user -> {
                            if (user.getCreateTime() != null) {
                                return java.time.Duration.between(user.getCreateTime(), LocalDateTime.now()).toDays();
                            }
                            return 0;
                        }, ExcelFormatter.integer()))
                .dataSource(DataSource.fromCollection(users))
                .export(response, "用户统计");
    }
    
    /**
     * 示例5：数据过滤和转换
     */
    public static void exportFilteredUsers(HttpServletResponse response,
                                         ServiceBase<?, User> userService) throws IOException {
        ExcelExporter.<User>create()
                .columns(cols -> cols
                        .add("姓名", User::getName)
                        .add("年龄", User::getAge, ExcelFormatter.integer())
                        .add("年龄段", user -> {
                            int age = user.getAge() != null ? user.getAge() : 0;
                            return switch (age / 10) {
                                case 0, 1 -> "青少年";
                                case 2, 3 -> "青年";
                                case 4, 5 -> "中年";
                                default -> "老年";
                            };
                        }))
                .dataSource(DataSource.fromService(userService)
                        .filter(user -> user.getAge() != null && user.getAge() >= 18)  // 过滤成年用户
                        .limit(1000))  // 限制导出数量
                .export(response, "成年用户");
    }
    
    /**
     * 示例6：快速导出
     */
    public static void quickExportExample(HttpServletResponse response) throws IOException {
        // 单列快速导出
        List<String> names = List.of("张三", "李四", "王五");
        ExcelExporter.quickExport(response, "姓名列表", "姓名", names);
        
        // 多列快速导出
        List<String> headers = List.of("姓名", "年龄", "城市");
        List<List<Object>> data = List.of(
                List.of("张三", 25, "北京"),
                List.of("李四", 30, "上海"),
                List.of("王五", 28, "广州")
        );
        ExcelExporter.quickExport(response, "用户信息", headers, data);
    }
    
    /**
     * 示例7：复杂查询条件
     */
    public static void exportWithComplexQuery(HttpServletResponse response,
                                            ServiceBase<?, User> userService,
                                            String nameKeyword,
                                            Integer minAge,
                                            Integer maxAge) throws IOException {
        ExcelExporter.<User>create()
                .sheet("查询结果")
                .columns(cols -> cols
                        .add("ID", User::getId)
                        .add("姓名", User::getName)
                        .add("年龄", User::getAge, ExcelFormatter.integer())
                        .add("邮箱", User::getEmail))
                .dataSource(DataSource.fromService(userService, wrapper -> {
                    if (nameKeyword != null && !nameKeyword.trim().isEmpty()) {
                        wrapper.like("name", nameKeyword.trim());
                    }
                    if (minAge != null) {
                        wrapper.ge("age", minAge);
                    }
                    if (maxAge != null) {
                        wrapper.le("age", maxAge);
                    }
                    wrapper.orderByDesc("create_time");
                }))
                .export(response, "查询结果");
    }
}
