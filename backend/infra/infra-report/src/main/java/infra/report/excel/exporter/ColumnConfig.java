package infra.report.excel.exporter;

import java.util.function.Function;

/**
 * Excel 列配置
 * 
 * @param <T> 数据类型
 */
public record ColumnConfig<T>(
        String header,
        Function<T, ?> extractor,
        ExcelFormatter<Object> formatter,
        Integer width
) {
    
    /**
     * 创建简单列配置
     */
    public static <T> ColumnConfig<T> of(String header, Function<T, ?> extractor) {
        return new ColumnConfig<>(header, extractor, ExcelFormatter.string(), null);
    }
    
    /**
     * 创建带格式化器的列配置
     */
    public static <T, R> ColumnConfig<T> of(String header, Function<T, R> extractor, ExcelFormatter<R> formatter) {
        @SuppressWarnings("unchecked")
        ExcelFormatter<Object> objectFormatter = (ExcelFormatter<Object>) formatter;
        return new ColumnConfig<>(header, extractor, objectFormatter, null);
    }
    
    /**
     * 创建带宽度的列配置
     */
    public static <T> ColumnConfig<T> of(String header, Function<T, ?> extractor, int width) {
        return new ColumnConfig<>(header, extractor, ExcelFormatter.string(), width);
    }
    
    /**
     * 创建完整列配置
     */
    public static <T, R> ColumnConfig<T> of(String header, Function<T, R> extractor, ExcelFormatter<R> formatter, int width) {
        @SuppressWarnings("unchecked")
        ExcelFormatter<Object> objectFormatter = (ExcelFormatter<Object>) formatter;
        return new ColumnConfig<>(header, extractor, objectFormatter, width);
    }
    
    /**
     * 设置列宽
     */
    public ColumnConfig<T> width(int width) {
        return new ColumnConfig<>(header, extractor, formatter, width);
    }
    
    /**
     * 设置格式化器
     */
    public <R> ColumnConfig<T> format(ExcelFormatter<R> newFormatter) {
        @SuppressWarnings("unchecked")
        ExcelFormatter<Object> objectFormatter = (ExcelFormatter<Object>) newFormatter;
        return new ColumnConfig<>(header, extractor, objectFormatter, width);
    }
    
    /**
     * 提取并格式化数据
     */
    public Object extractAndFormat(T data) {
        if (data == null) return "";
        Object extracted = extractor.apply(data);
        return formatter.apply(extracted);
    }
}
