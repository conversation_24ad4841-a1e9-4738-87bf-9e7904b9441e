package infra.report.excel;

import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDateTime;

/**
* Excel 导入演示
* 展示新的现代化导入 API 使用方法
  */
  public class ExcelImportDemo {

  // 示例数据类
  public record User(Long id, String name, Integer age, String email, Boolean active, LocalDateTime createTime) {
  // 可变版本用于导入
  public static class Mutable {
  private Long id;
  private String name;
  private Integer age;
  private String email;
  private Boolean active;
  private LocalDateTime createTime;

           public Mutable() {}
           
           // getters and setters
           public Long getId() { return id; }
           public void setId(Long id) { this.id = id; }
           public String getName() { return name; }
           public void setName(String name) { this.name = name; }
           public Integer getAge() { return age; }
           public void setAge(Integer age) { this.age = age; }
           public String getEmail() { return email; }
           public void setEmail(String email) { this.email = email; }
           public Boolean getActive() { return active; }
           public void setActive(Boolean active) { this.active = active; }
           public LocalDateTime getCreateTime() { return createTime; }
           public void setCreateTime(LocalDateTime createTime) { this.createTime = createTime; }
           
           // 转换为不可变记录
           public User toRecord() {
               return new User(id, name, age, email, active, createTime);
           }
           
           @Override
           public String toString() {
               return String.format("User{id=%d, name='%s', age=%d, email='%s', active=%s}", 
                       id, name, age, email, active);
           }
       }
  }

  /**
    * 演示基本导入用法
      */
      public static ImportResult<User.Mutable> basicImport(MultipartFile file) throws IOException {
      return ExcelImporter.create(User.Mutable::new)
      .columns(cols -> cols
      .required("ID", ExcelParser.longValue(), User.Mutable::setId)
      .required("姓名", ExcelParser.string(), User.Mutable::setName)
      .add("年龄", ExcelParser.integer(), User.Mutable::setAge)
      .add("邮箱", ExcelParser.string(), User.Mutable::setEmail)
      .add("状态", ExcelParser.bool("启用", "禁用"), User.Mutable::setActive)
      .add("创建时间", ExcelParser.dateTime(), User.Mutable::setCreateTime))
      .importFrom(file);
      }

  /**
    * 演示容错导入
      */
      public static ImportResult<User.Mutable> tolerantImport(MultipartFile file) throws IOException {
      return ExcelImporter.create(User.Mutable::new)
      .columns(cols -> cols
      .add("ID", ExcelParser.longValue().optional(), User.Mutable::setId)
      .required("姓名", ExcelParser.string(), User.Mutable::setName)
      .withDefault("年龄", ExcelParser.integer().orDefault(0), User.Mutable::setAge, 18)
      .add("邮箱", ExcelParser.string().optional(), User.Mutable::setEmail)
      .withDefault("状态", ExcelParser.bool().orDefault(true), User.Mutable::setActive, true))
      .skipEmptyRows(true)
      .importFrom(file);
      }

  /**
    * 演示流式导入（适合大文件）
      */
      public static void streamImport(MultipartFile file) throws IOException {
      ExcelImporter.create(User.Mutable::new)
      .columns(cols -> cols
      .add("ID", ExcelParser.longValue(), User.Mutable::setId)
      .add("姓名", ExcelParser.string(), User.Mutable::setName)
      .add("年龄", ExcelParser.integer(), User.Mutable::setAge))
      .batchSize(1000)
      .importStream(file.getInputStream(),
      batch -> {
      // 批量处理逻辑
      System.out.println("处理批次，大小: " + batch.size());
      // 这里可以批量保存到数据库
      // userService.saveBatch(batch);
      },
      error -> {
      // 错误处理逻辑
      System.err.println("导入错误: " + error);
      });
      }

  /**
    * 演示自定义解析
      */
      public static ImportResult<User.Mutable> customParsingImport(MultipartFile file) throws IOException {
      return ExcelImporter.create(User.Mutable::new)
      .columns(cols -> cols
      .add("用户编号", ExcelParser.longValue(), User.Mutable::setId)
      .add("用户姓名", ExcelParser.string(), User.Mutable::setName)
      .add("用户年龄", ExcelParser.custom(value -> {
      // 处理 "25岁" 这样的格式
      if (value == null) return null;
      String str = value.toString().trim();
      if (str.endsWith("岁")) {
      str = str.substring(0, str.length() - 1);
      }
      return Integer.valueOf(str);
      }), User.Mutable::setAge)
      .add("账户状态", ExcelParser.custom(value -> {
      // 处理中文状态
      if (value == null) return false;
      String str = value.toString().trim();
      return "正常".equals(str) || "启用".equals(str) || "活跃".equals(str);
      }), User.Mutable::setActive))
      .importFrom(file);
      }

  /**
    * 演示结果处理
      */
      public static void handleImportResult(MultipartFile file) throws IOException {
      ImportResult<User.Mutable> result = ExcelImporter.create(User.Mutable::new)
      .columns(cols -> cols
      .required("姓名", ExcelParser.string(), User.Mutable::setName)
      .add("年龄", ExcelParser.integer(), User.Mutable::setAge))
      .importFrom(file);

      // 检查导入结果
      if (result.isSuccess()) {
      System.out.println("导入成功！共导入 " + result.successCount() + " 条记录");

           // 处理成功的数据
           result.successData().forEach(user -> {
               System.out.println("导入用户: " + user);
               // 保存到数据库等业务逻辑
           });
      } else {
      System.out.println("导入完成，但有错误：");
      System.out.println(result.getErrorSummary());

           // 处理成功的数据
           if (!result.successData().isEmpty()) {
               System.out.println("成功导入 " + result.successCount() + " 条记录");
               // 保存成功的数据
           }
           
           // 处理错误
           if (result.hasErrors()) {
               System.out.println("错误详情：");
               result.errors().forEach(error -> {
                   System.out.println("  " + error);
               });
           }
      }
      }

  /**
    * 演示多工作表导入
      */
      public static ImportResult<User.Mutable> multiSheetImport(MultipartFile file) throws IOException {
      return ExcelImporter.create(User.Mutable::new)
      .sheet("用户数据")  // 指定工作表名称
      .columns(cols -> cols
      .add("ID", ExcelParser.longValue(), User.Mutable::setId)
      .add("姓名", ExcelParser.string(), User.Mutable::setName))
      .importFrom(file);
      }

  /**
    * 演示使用便捷方法
      */
      public static ImportResult<User.Mutable> quickImport(MultipartFile file) throws IOException {
      // 使用 ExcelExport 的便捷方法
      return ExcelExport.importer(User.Mutable::new)
      .columns(cols -> cols
      .add("姓名", ExcelParser.string(), User.Mutable::setName)
      .add("年龄", ExcelParser.integer(), User.Mutable::setAge))
      .importFrom(file);
      }
      }
