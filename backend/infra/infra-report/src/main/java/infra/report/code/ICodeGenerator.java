package infra.report.code;

import java.awt.image.BufferedImage;
import java.io.OutputStream;

/**
 * 二维码/条码生成统一接口
 */
public interface ICodeGenerator {
    /**
     * 生成BufferedImage
     */
    BufferedImage generateImage(String data, CodeRenderOptions options);

    /**
     * 直接输出到流
     */
    void generateToStream(String data, CodeRenderOptions config, OutputStream outputStream);

    /**
     * 生成字节数组
     */
    byte[] generateBytes(String data, CodeRenderOptions options);
}
