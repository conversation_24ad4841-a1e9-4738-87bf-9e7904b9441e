package infra.report.excel;

import org.springframework.lang.NonNull;

import java.util.ArrayList;
import java.util.List;

/**
 * Excel 导入结果
 * 
 * @param <T> 导入的数据类型
 */
public record ImportResult<T>(
        List<T> successData,
        List<ImportError> errors,
        int totalRows,
        int successCount,
        int errorCount
) {
    
    /**
     * 创建成功结果
     */
    public static <T> ImportResult<T> success(List<T> data) {
        return new ImportResult<>(data, List.of(), data.size(), data.size(), 0);
    }
    
    /**
     * 创建带错误的结果
     */
    public static <T> ImportResult<T> withErrors(List<T> successData, List<ImportError> errors) {
        int totalRows = successData.size() + errors.size();
        return new ImportResult<>(successData, errors, totalRows, successData.size(), errors.size());
    }
    
    /**
     * 是否完全成功
     */
    public boolean isSuccess() {
        return errors.isEmpty();
    }
    
    /**
     * 是否有错误
     */
    public boolean hasErrors() {
        return !errors.isEmpty();
    }
    
    /**
     * 获取成功率
     */
    public double getSuccessRate() {
        return totalRows == 0 ? 0.0 : (double) successCount / totalRows;
    }
    
    /**
     * 获取错误摘要
     */
    public String getErrorSummary() {
        if (errors.isEmpty()) {
            return "导入成功";
        }
        
        StringBuilder sb = new StringBuilder();
        sb.append(String.format("导入完成，成功%d行，失败%d行", successCount, errorCount));
        
        if (errors.size() <= 5) {
            sb.append("，错误详情：");
            for (ImportError error : errors) {
                sb.append("\n").append(error.toString());
            }
        } else {
            sb.append("，前5个错误：");
            for (int i = 0; i < 5; i++) {
                sb.append("\n").append(errors.get(i).toString());
            }
            sb.append(String.format("\n...还有%d个错误", errors.size() - 5));
        }
        
        return sb.toString();
    }
    
    /**
     * 导入错误信息
     */
    public record ImportError(
            int rowIndex,
            String message,
            Throwable cause
    ) {
        
        /**
         * 创建错误信息
         */
        public static ImportError of(int rowIndex, String message) {
            return new ImportError(rowIndex, message, null);
        }
        
        /**
         * 创建带异常的错误信息
         */
        public static ImportError of(int rowIndex, String message, Throwable cause) {
            return new ImportError(rowIndex, message, cause);
        }
        
        @Override
        @NonNull
        public String toString() {
            return String.format("第%d行: %s", rowIndex + 1, message);
        }
    }
    
    /**
     * 导入结果构建器
     */
    public static class Builder<T> {
        private final List<T> successData = new ArrayList<>();
        private final List<ImportError> errors = new ArrayList<>();
        
        /**
         * 添加成功数据
         */
        public Builder<T> addSuccess(T data) {
            successData.add(data);
            return this;
        }
        
        /**
         * 添加成功数据列表
         */
        public Builder<T> addSuccess(List<T> data) {
            successData.addAll(data);
            return this;
        }
        
        /**
         * 添加错误
         */
        public Builder<T> addError(int rowIndex, String message) {
            errors.add(ImportError.of(rowIndex, message));
            return this;
        }
        
        /**
         * 添加错误
         */
        public Builder<T> addError(int rowIndex, String message, Throwable cause) {
            errors.add(ImportError.of(rowIndex, message, cause));
            return this;
        }
        
        /**
         * 添加错误
         */
        public Builder<T> addError(ImportError error) {
            errors.add(error);
            return this;
        }
        
        /**
         * 构建结果
         */
        public ImportResult<T> build() {
            return ImportResult.withErrors(new ArrayList<>(successData), new ArrayList<>(errors));
        }
    }
}
