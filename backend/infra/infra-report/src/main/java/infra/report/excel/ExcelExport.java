package infra.report.excel;

import cn.idev.excel.ExcelWriter;
import cn.idev.excel.FastExcel;
import cn.idev.excel.write.metadata.WriteSheet;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import infra.core.web.ServletUtil;
import infra.domain.entity.IdEntity;
import infra.domain.service.ServiceBase;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.function.Consumer;
import java.util.function.Function;

/**
 * Excel导出
 */
public class ExcelExport {
    /**
     * 基于ServiceBase快速列表导出
     */
    public static <T extends IdEntity> void exportList(HttpServletResponse response,
                                                       ServiceBase<?, T> service,
                                                       Consumer<QueryWrapper<T>> queryWrapper,
                                                       List<List<String>> headers,
                                                       Function<T, List<Object>> dataConverter,
                                                       String fileNamePrefix) throws IOException {

        String fileName = fileNamePrefix + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")) + ".xlsx";
        try (ServletOutputStream outputStream = ServletUtil.writeFile(response, fileName, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")) {
            // 创建 ExcelWriter 并设置表头
            try (ExcelWriter writer = FastExcel.write(outputStream).head(headers).build()) {

                // 创建 Sheet 写入器 - 使用正确的构造函数
                WriteSheet writeSheet = new WriteSheet();
                writeSheet.setSheetName("Sheet1");

                // 使用流式查询处理数据
                service.streamQuery(queryWrapper, item -> {
                    // 使用 writer 写入数据
                    writer.write(Collections.singletonList(dataConverter.apply(item)), writeSheet);
                });
            }
        }
    }
}
