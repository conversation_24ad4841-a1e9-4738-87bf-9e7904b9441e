package infra.report.excel.export;

import cn.idev.excel.ExcelWriter;
import cn.idev.excel.FastExcel;
import cn.idev.excel.write.metadata.WriteSheet;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import infra.core.web.ServletUtil;
import infra.domain.entity.IdEntity;
import infra.domain.service.ServiceBase;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.function.Consumer;
import java.util.function.Function;

/**
 * Excel导出工具类
 *
 *
 * <h3>现代API使用示例：</h3>
 * <pre>{@code
 * // 基本用法
 * ExcelExporter.<User>create()
 *     .sheet("用户列表")
 *     .columns(cols -> cols
 *         .add("姓名", User::getName)
 *         .add("年龄", User::getAge, ExcelFormatter.integer())
 *         .add("创建时间", User::getCreateTime, ExcelFormatter.dateTime()))
 *     .dataSource(DataSource.fromService(userService, wrapper -> wrapper.eq("status", 1)))
 *     .export(response, "用户数据");
 *
 * // 从集合导出
 * ExcelExporter.<String>create()
 *     .columns(cols -> cols.add("数据", String::toString))
 *     .dataSource(DataSource.fromCollection(dataList))
 *     .export(response, "数据列表");
 * }</pre>
 */
public class ExcelExport {

    /**
     * 基于ServiceBase快速列表导出
     */
    public static <T extends IdEntity> void exportList(HttpServletResponse response,
                                                       ServiceBase<?, T> service,
                                                       Consumer<QueryWrapper<T>> queryWrapper,
                                                       List<List<String>> headers,
                                                       Function<T, List<Object>> dataConverter,
                                                       String fileNamePrefix) throws IOException {

        String fileName = fileNamePrefix + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")) + ".xlsx";
        try (ServletOutputStream outputStream = ServletUtil.writeFile(response, fileName, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")) {
            // 创建 ExcelWriter 并设置表头
            try (ExcelWriter writer = FastExcel.write(outputStream).head(headers).build()) {

                // 创建 Sheet 写入器 - 使用正确的构造函数
                WriteSheet writeSheet = new WriteSheet();
                writeSheet.setSheetName("Sheet1");

                // 使用流式查询处理数据
                service.streamQuery(queryWrapper, item -> {
                    // 使用 writer 写入数据
                    writer.write(Collections.singletonList(dataConverter.apply(item)), writeSheet);
                });
            }
        }
    }

    /**
     * 创建 Excel 导出器
     *
     * @param <T> 数据类型
     * @return Excel 导出器实例
     */
    public static <T> ExcelExporter<T> exporter() {
        return ExcelExporter.create();
    }

    /**
     * 创建 Excel 导入器
     *
     * @param <T> 数据类型
     * @param objectFactory 对象工厂
     * @return Excel 导入器实例
     */
    public static <T> ExcelImporter<T> importer(java.util.function.Supplier<T> objectFactory) {
        return ExcelImporter.create(objectFactory);
    }

    /**
     * 快速导出单列数据
     */
    public static <T> void quickExport(HttpServletResponse response, String fileName,
                                       String header, List<T> data) throws IOException {
        ExcelExporter.quickExport(response, fileName, header, data);
    }

    /**
     * 快速导出多列数据
     */
    public static void quickExport(HttpServletResponse response, String fileName,
                                   List<String> headers, List<List<Object>> data) throws IOException {
        ExcelExporter.quickExport(response, fileName, headers, data);
    }
}
