package infra.sms.config;

import infra.core.exception.ConfigException;
import infra.core.text.Str;
import infra.sms.core.ISms;
import infra.sms.impl.AliyunSms;
import infra.sms.impl.LogSms;
import infra.sms.impl.TencentSms;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;

/**
 * 短信自动配置
 */
@AutoConfiguration
@EnableConfigurationProperties(SmsConfigProperties.class)
@ConditionalOnProperty(prefix = "app.sms", name = "provider")
@Slf4j
public class SmsAutoConfiguration {
    /**
     * 根据配置创建短信服务实例
     */
    @Bean
    @ConditionalOnMissingBean(ISms.class)
    public ISms infraSms(SmsConfigProperties config) {
        log.info("[配置] 配置sms提供程序 - {}", config.getProvider());
        return createSms(config);
    }

    /**
     * 创建短信实例
     */
    private ISms createSms(SmsConfigProperties config) {
        if (Str.isEmpty(config.getProvider())) {
            throw new ConfigException("未配置app.sms.provider参数");
        }

        return switch (config.getProvider().toLowerCase()) {
            case "aliyun" -> new AliyunSms(config.getAliyun());
            case "tencent" -> new TencentSms(config.getTencent());
            case "log" -> new LogSms();
            default -> throw new IllegalArgumentException("暂未支持的SMS提供实现: " + config.getProvider());
        };
    }
}
