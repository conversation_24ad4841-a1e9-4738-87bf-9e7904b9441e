package infra.sms.impl;

import infra.core.common.Result;
import infra.sms.core.ISms;
import lombok.extern.slf4j.Slf4j;

/**
 * 日志短信服务实现(用于测试)
 */
@Slf4j
public class LogSms implements ISms {
    @Override
    public Result<String> send(String number, String template, String... params) {
        log.info("[短信发送] 接收号码: {}, 模板: {}, 参数: {}", number, template, String.join(",", params));
        return Result.okData(String.join(",", params));
    }
}
