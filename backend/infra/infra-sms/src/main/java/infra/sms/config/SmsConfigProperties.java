package infra.sms.config;

import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

/**
 * 短信配置属性
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Validated
@ConfigurationProperties(prefix = "app.sms")
public class SmsConfigProperties {
    @NotBlank
    private String provider;
    // 阿里云配置
    private AliyunConfig aliyun = new AliyunConfig();
    // 腾讯云配置
    private TencentConfig tencent = new TencentConfig();

    /**
     * 阿里云配置
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AliyunConfig {
        private String accessKeyId;
        private String accessKeySecret;
        private String signName;
        private String endpoint = "dysmsapi.aliyuncs.com";
    }

    /**
     * 腾讯云配置
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TencentConfig {
        private String secretId;
        private String secretKey;
        private String sdkAppId;
        private String signName;
        private String endpoint = "sms.tencentcloudapi.com";
        private String region = "ap-beijing";
    }
}
