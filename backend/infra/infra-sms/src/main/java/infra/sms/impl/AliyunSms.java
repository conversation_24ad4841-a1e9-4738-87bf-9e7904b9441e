package infra.sms.impl;

import com.aliyun.dysmsapi20170525.Client;
import com.aliyun.dysmsapi20170525.models.SendSmsRequest;
import com.aliyun.dysmsapi20170525.models.SendSmsResponse;
import com.aliyun.teaopenapi.models.Config;
import com.fasterxml.jackson.databind.ObjectMapper;
import infra.core.common.Result;
import infra.core.exception.ConfigException;
import infra.core.text.Str;
import infra.sms.config.SmsConfigProperties;
import infra.sms.core.ISms;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

/**
 * 阿里云短信服务实现
 */
@Slf4j
public class AliyunSms implements ISms {
    private final SmsConfigProperties.AliyunConfig config;
    private final Client client;
    private final ObjectMapper objectMapper;

    public AliyunSms(SmsConfigProperties.AliyunConfig config) {
        this.config = validateConfig(config);
        this.client = createClient();
        this.objectMapper = new ObjectMapper();
    }

    /**
     * 发送短信
     *
     * @param number   接收号码
     * @param template 模板
     * @param params   短信参数
     * @return 发送结果
     */
    @Override
    public Result<String> send(String number, String template, String... params) {
        if (Str.isEmpty(number)) {
            return Result.fail("接收号码不能为空");
        }
        if (Str.isEmpty(template)) {
            return Result.fail("发送模板不能为空");
        }

        try {
            SendSmsRequest request = new SendSmsRequest().setPhoneNumbers(number).setSignName(config.getSignName()).setTemplateCode(template);

            // 构建模板参数
            if (params != null && params.length > 0) {
                Map<String, String> templateParams = new HashMap<>();
                for (int i = 0; i < params.length; i++) {
                    templateParams.put("param" + (i + 1), params[i]);
                }
                request.setTemplateParam(objectMapper.writeValueAsString(templateParams));
            }

            SendSmsResponse response = client.sendSms(request);

            if ("OK".equals(response.getBody().getCode())) {
                return Result.OK_STRING;
            } else {
                var responseBody = response.getBody();
                log.error("[阿里云短信] 发送失败 - 手机号: {}, 错误码: {}, 错误信息: {}", number, responseBody.getCode(), responseBody.getMessage());
                return Result.fail("短信发送失败: " + responseBody.getMessage());
            }
        } catch (Exception e) {
            log.error("[阿里云短信] 发送失败: {}", e.getMessage(), e);
            return Result.fail("短信发送失败: " + e.getMessage());
        }
    }

    /**
     * 创建阿里云SMS客户端
     */
    private Client createClient() {
        try {
            Config config = new Config().setAccessKeyId(this.config.getAccessKeyId()).setAccessKeySecret(this.config.getAccessKeySecret()).setEndpoint(this.config.getEndpoint());

            return new Client(config);
        } catch (Exception e) {
            log.error("[阿里云短信] 客户端初始化失败: {}", e.getMessage(), e);
            throw new ConfigException("阿里云短信客户端初始化失败", e);
        }
    }

    /**
     * 验证配置参数
     */
    private SmsConfigProperties.AliyunConfig validateConfig(SmsConfigProperties.AliyunConfig config) {
        if (Str.isEmpty(config.getAccessKeyId()) || Str.isEmpty(config.getAccessKeySecret()) || Str.isEmpty(config.getSignName()) || Str.isEmpty(config.getEndpoint())) {
            throw new ConfigException("阿里云短信配置不完整");
        }

        return config;
    }
}
