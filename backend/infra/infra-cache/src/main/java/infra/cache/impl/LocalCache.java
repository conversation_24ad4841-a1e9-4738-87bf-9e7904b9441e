package infra.cache.impl;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.Expiry;
import infra.cache.core.ICache;
import infra.core.text.Str;
import lombok.extern.slf4j.Slf4j;
import org.jspecify.annotations.NullMarked;

import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 基于Caffeine的本地缓存实现
 *
 * <p>
 * 提供高性能的本地缓存功能。
 * 该实现是线程安全的，适用于单机环境下的缓存需求。
 */
@Slf4j
public class LocalCache implements ICache {

    /**
     * 默认最大缓存条目数
     */
    public static final int DEFAULT_MAX_SIZE = 10_000;

    /**
     * 底层Caffeine缓存实例
     */
    private final Cache<String, Object> cache;

    /**
     * 默认构造函数，使用默认配置
     * <p>
     * 默认最大容量为{@link #DEFAULT_MAX_SIZE}
     */
    public LocalCache() {
        this(DEFAULT_MAX_SIZE);
    }

    /**
     * 自定义容量的构造函数
     *
     * @param maxCapacity 最大缓存容量（防止内存溢出）
     * @throws IllegalArgumentException 当参数无效时抛出
     */
    public LocalCache(int maxCapacity) {
        if (maxCapacity <= 0) {
            throw new IllegalArgumentException("最大容量必须大于0");
        }

        this.cache = Caffeine.newBuilder()
                .maximumSize(maxCapacity)
                .expireAfter(new AbsoluteExpiry())
                .build();
    }

    /**
     * 获取缓存中的值
     *
     * @param key  缓存键
     * @param type 值类型的Class对象
     * @param <T>  值类型
     * @return 缓存值，不存在时返回null
     * @throws IllegalArgumentException 当类型转换失败时抛出
     */
    @Override
    public <T> T get(String key, Class<T> type) {
        Object value = cache.getIfPresent(key);
        if (value == null) {
            return null;
        }

        try {
            return type.cast(((CacheEntry) value).value());
        } catch (ClassCastException e) {
            throw new IllegalArgumentException("该缓存不是指定类型: " + type, e);
        }
    }

    /**
     * 设置缓存
     *
     * @param key           缓存键
     * @param value         缓存值
     * @param expireSeconds 过期时间（秒）
     * @param <T>           值类型
     * @throws IllegalArgumentException 当参数无效时抛出
     */
    @Override
    public <T> void set(String key, T value, long expireSeconds) {
        if (Str.isEmpty(key)) {
            return;
        }
        if (value == null) {
            log.warn("尝试设置null值到缓存，key: {}", key);
            return;
        }

        cache.put(key, new CacheEntry(value, expireSeconds));
    }

    /**
     * 批量设置缓存
     *
     * @param kvMap         键值对映射
     * @param expireSeconds 统一的过期时间（秒）
     * @param <T>           值类型
     */
    @Override
    public <T> void setBatch(Map<String, T> kvMap, long expireSeconds) {
        if (kvMap == null || kvMap.isEmpty()) {
            return;
        }

        Map<String, CacheEntry> entries = kvMap.entrySet().stream()
                .filter(entry -> entry.getValue() != null)
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> new CacheEntry(entry.getValue(), expireSeconds)
                ));

        // 批量写入缓存
        cache.putAll(entries);
    }

    /**
     * 删除指定键的缓存
     *
     * @param key 缓存键
     */
    @Override
    public void delete(String key) {
        cache.invalidate(key);
    }

    /**
     * 批量删除缓存
     *
     * @param keys 缓存键的集合
     */
    @Override
    public void deleteBatch(Iterable<String> keys) {
        cache.invalidateAll(keys);
    }

    /**
     * 按前缀删除缓存
     * <p>
     * 删除所有以指定前缀开头的缓存键
     * </p>
     *
     * @param prefix 键前缀
     * @throws IllegalArgumentException 当前缀为null时抛出
     */
    @Override
    public void deletePattern(String prefix) {
        var removeKeys = cache.asMap().keySet().stream()
                .filter(key -> key.startsWith(prefix))
                .toList();
        cache.invalidateAll(removeKeys);
    }

    /**
     * 判断缓存是否存在
     *
     * @param key 缓存键
     * @return true表示存在，false表示不存在
     */
    @Override
    public boolean exists(String key) {
        return cache.getIfPresent(key) != null;
    }

    /**
     * 清空所有缓存
     * <p>
     * 谨慎使用，这会删除该缓存实例中的所有数据
     * </p>
     */
    @Override
    public void clear() {
        cache.invalidateAll();
    }

    /**
     * 获取当前缓存中的条目数量
     *
     * @return 缓存条目数量
     */
    public long size() {
        return cache.estimatedSize();
    }

    /**
     * 获取底层Caffeine缓存实例
     * <p>
     * 可用于执行更高级的缓存操作
     * </p>
     *
     * @return Caffeine缓存实例
     */
    public Cache<String, Object> getNativeCache() {
        return cache;
    }

    /**
     * 自定义过期策略实现
     * <p>
     * 基于每个缓存条目中存储的过期时间决定何时过期
     * </p>
     */
    @NullMarked
    private static class AbsoluteExpiry implements Expiry<String, Object> {
        /**
         * 创建条目后的过期时间
         */
        @Override
        public long expireAfterCreate(String key, Object value, long currentTime) {
            return TimeUnit.SECONDS.toNanos(((CacheEntry) value).expireSeconds);
        }

        /**
         * 更新条目后的过期时间
         * 使用新的过期时间
         */
        @Override
        public long expireAfterUpdate(String key, Object value, long currentTime, long currentDuration) {
            return TimeUnit.SECONDS.toNanos(((CacheEntry) value).expireSeconds);
        }

        /**
         * 读取条目后的过期时间
         * 保持原有过期时间不变
         */
        @Override
        public long expireAfterRead(String key, Object value, long currentTime, long currentDuration) {
            return currentDuration;
        }
    }

    /**
     * 缓存条目元数据包装
     *
     * @param value         缓存的实际值
     * @param expireSeconds 过期时间（秒）
     */
    private record CacheEntry(Object value, long expireSeconds) {
    }
}