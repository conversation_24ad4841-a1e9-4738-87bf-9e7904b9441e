package infra.cache.impl;

import infra.cache.core.ICounter;

import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 本地计数器，仅适用于单机环境
 */
public class LocalCounter implements ICounter {
    /**
     * 计数器存储
     */
    private final ConcurrentMap<String, AtomicLong> counters = new ConcurrentHashMap<>();
    /**
     * 清理线程
     */
    private final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    /**
     * 是否已关闭
     */
    private volatile boolean closed = false;

    /**
     * 初始化计数器
     *
     * @param key           键
     * @param expireSeconds 计数器有效期(秒)，小于等于0为永不过期
     */
    @Override
    public void init(String key, long expireSeconds) {
        checkClosed();

        counters.computeIfAbsent(key, k -> {
            var counter = new AtomicLong(0L);
            if (expireSeconds > 0) {
                scheduler.schedule(() -> counters.remove(key), expireSeconds, TimeUnit.SECONDS);
            }
            return counter;
        });
    }

    /**
     * 增加指定数量的值
     *
     * @param key    键
     * @param amount 增加的数量
     * @return 增加后的值
     */
    @Override
    public long incr(String key, long amount) {
        checkClosed();
        var counter = counters.get(key);
        if (counter == null) {
            throw new IllegalArgumentException("计数器不存在:" + key);
        }
        return counter.addAndGet(amount);
    }

    /**
     * 减少指定数量的值
     *
     * @param key    键
     * @param amount 减少的数量
     * @return 减少后的值
     */
    @Override
    public long decr(String key, long amount) {
        return incr(key, -amount);
    }

    /**
     * 获取当前计数器值
     *
     * @param key 键
     * @return 当前值，键不存在时返回0
     */
    @Override
    public long get(String key) {
        checkClosed();
        var counter = counters.get(key);
        return counter == null ? 0 : counter.get();
    }

    /**
     * 删除计数器
     *
     * @param key 键
     */
    @Override
    public void delete(String key) {
        checkClosed();
        counters.remove(key);
    }

    /**
     * 关闭计数器
     */
    @Override
    public void close() {
        scheduler.shutdownNow();
        counters.clear();
        closed = true;
    }

    /**
     * 检查是否已关闭
     */
    private void checkClosed() {
        if (closed) {
            throw new IllegalStateException("计数器已关闭");
        }
    }
}
