package infra.cache.impl;

import infra.cache.core.ICounter;
import infra.core.text.Str;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RedissonClient;

import java.time.Duration;

/**
 * 基于 Redis 的计数器实现
 */
@Slf4j
@AllArgsConstructor
public class RedisCounter implements ICounter {
    /**
     * Redisson 客户端
     */
    private final RedissonClient redissonClient;

    /**
     * 初始化计数器
     *
     * @param key           键
     * @param expireSeconds 计数器有效期(秒)，小于等于0为永不过期，需要手动删除
     */
    @Override
    public void init(String key, long expireSeconds) {
        if (Str.isEmpty(key)) {
            return;
        }

        RAtomicLong atomicLong = redissonClient.getAtomicLong(key);
        atomicLong.set(0);
        if (expireSeconds > 0) {
            atomicLong.expire(Duration.ofSeconds(expireSeconds));
        }

        log.debug("初始化计数器成功，key: {}, 过期时间: {}秒", key, expireSeconds);
    }

    /**
     * 增加指定数量的值
     *
     * @param key    键
     * @param amount 增加的数量
     * @return 增加后的值
     */
    @Override
    public long incr(String key, long amount) {
        if (Str.isEmpty(key)) {
            log.warn("对计数器进行为空的key操作");
            return 0;
        }

        RAtomicLong atomicLong = redissonClient.getAtomicLong(key);
        return atomicLong.addAndGet(amount);
    }

    /**
     * 减少指定数量的值
     *
     * @param key    键
     * @param amount 减少的数量
     * @return 减少后的值
     */
    @Override
    public long decr(String key, long amount) {
        return incr(key, -amount);
    }

    /**
     * 获取当前计数器值
     *
     * @param key 键
     * @return 当前值，键不存在时返回0
     */
    @Override
    public long get(String key) {
        if (Str.isEmpty(key)) {
            return 0;
        }

        RAtomicLong atomicLong = redissonClient.getAtomicLong(key);
        return atomicLong.get();
    }

    /**
     * 删除计数器
     *
     * @param key 键
     */
    @Override
    public void delete(String key) {
        if (Str.isEmpty(key)) {
            return;
        }

        RAtomicLong atomicLong = redissonClient.getAtomicLong(key);
        atomicLong.delete();
    }

    /**
     * 释放计数器
     */
    @Override
    public void close() {
        // 无需释放
    }
}
