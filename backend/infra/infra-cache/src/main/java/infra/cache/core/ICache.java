package infra.cache.core;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.function.Supplier;

/**
 * 通用缓存操作接口
 */
public interface ICache {
    Logger log = LoggerFactory.getLogger(ICache.class);

    /**
     * 获取缓存中的值
     *
     * @param key  缓存键
     * @param type 值类型的Class对象
     * @param <T>  值类型
     * @return 缓存值，不存在时返回null
     */
    <T> T get(String key, Class<T> type);

    /**
     * 设置缓存
     *
     * @param key           缓存键
     * @param value         缓存值
     * @param expireSeconds 过期时间（秒）
     * @param <T>           值类型
     */
    <T> void set(String key, T value, long expireSeconds);

    /**
     * 批量设置缓存(可用于缓存预热)
     *
     * @param kvMap         键值对映射
     * @param expireSeconds 统一的过期时间（秒）
     * @param <T>           值类型
     * @throws IllegalArgumentException 当过期时间小于等于0时抛出
     */
    <T> void setBatch(Map<String, T> kvMap, long expireSeconds);

    /**
     * 删除指定键的缓存
     *
     * @param key 缓存键
     */
    void delete(String key);

    /**
     * 批量删除缓存
     *
     * @param keys 缓存键的集合
     */
    void deleteBatch(Iterable<String> keys);

    /**
     * 按前缀删除缓存
     * <p>
     * 删除所有以指定前缀开头的缓存键
     *
     * @param prefix 键前缀
     */
    void deletePattern(String prefix);

    /**
     * 判断缓存是否存在
     *
     * @param key 缓存键
     * @return true表示存在，false表示不存在
     */
    boolean exists(String key);

    /**
     * 清空所有缓存
     * <p>
     * 谨慎使用，这会删除该缓存实例中的所有数据
     */
    void clear();

    /**
     * 设置缓存值（默认过期时间为10分钟）
     *
     * @param key   缓存键
     * @param value 缓存值
     */
    default void set(String key, Object value) {
        set(key, value, 600);
    }

    /**
     * 获取缓存值，如果不存在则通过回调函数获取并设置缓存
     *
     * @param key           缓存键
     * @param type          值类型的Class对象
     * @param callback      当缓存不存在时的回调函数
     * @param expireSeconds 过期时间（秒）
     * @param <T>           值类型
     * @return 缓存值或回调函数的返回值
     */
    default <T> T getOrSet(String key, Class<T> type, Supplier<T> callback, long expireSeconds) {
        try {
            T value = get(key, type);
            if (value == null) {
                value = callback.get();
                if (value != null) {
                    set(key, value, expireSeconds);
                }
            }
            return value;
        } catch (Exception e) {
            log.error("缓存获取或设置异常，使用降级方式返回, key: {}", key, e);
            return callback.get();
        }
    }

    /**
     * 获取缓存值，如果不存在则通过回调函数获取并设置缓存（使用默认过期时间10分钟）
     *
     * @param key      缓存键
     * @param type     值类型的Class对象
     * @param callback 当缓存不存在时的回调函数
     * @param <T>      值类型
     * @return 缓存值或回调函数的返回值
     */
    default <T> T getOrSet(String key, Class<T> type, Supplier<T> callback) {
        return getOrSet(key, type, callback, 600);
    }
}
