package infra.cache.impl;

import infra.cache.core.ICache;
import infra.core.text.Str;
import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.redisson.api.listener.PatternMessageListener;
import org.springframework.beans.factory.DisposableBean;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 二级缓存实现（本地缓存 + Redis缓存）
 * <p>
 * 提供高性能的本地缓存和分布式Redis缓存的组合方案。
 * 通过Redis发布/订阅机制保证多节点间的缓存一致性。
 */
@Slf4j
public class TwoLevelCache implements ICache, DisposableBean {
    // 本地缓存实例
    private final LocalCache localCache;
    // Redis缓存实例
    private final RedisCache redisCache;
    // Redis客户端（用于发布/订阅）
    private final RedissonClient redissonClient;
    // 缓存实例标识（用于区分不同的缓存实例）
    private final String instanceId;
    // 缓存失效消息频道
    private final String cacheInvalidateChannel;
    // 消息监听器注册表
    private final Map<String, Integer> listenerRegistry = new ConcurrentHashMap<>();


    /**
     * 构造函数
     *
     * @param localCache             本地缓存实例
     * @param redisCache             Redis缓存实例
     * @param redissonClient         Redis客户端
     * @param instanceId             缓存实例标识
     * @param cacheInvalidateChannel 缓存失效消息频道
     */
    public TwoLevelCache(LocalCache localCache, RedisCache redisCache, RedissonClient redissonClient, String instanceId, String cacheInvalidateChannel) {
        this.localCache = localCache;
        this.redisCache = redisCache;
        this.redissonClient = redissonClient;
        this.instanceId = instanceId;
        this.cacheInvalidateChannel = cacheInvalidateChannel;

        // 注册缓存失效监听器
        registerInvalidationListener();
    }

    /**
     * 获取缓存中的值
     * <p>
     * 读取策略：本地缓存 -> Redis缓存 -> 返回null
     *
     * @param key  缓存键
     * @param type 值类型的Class对象
     * @param <T>  值类型
     * @return 缓存值，不存在时返回null
     */
    @Override
    public <T> T get(String key, Class<T> type) {
        if (Str.isEmpty(key)) {
            return null;
        }

        // 先从本地缓存获取
        T value = localCache.get(key, type);
        if (value != null) {
            log.debug("从本地缓存获取值成功：{}", key);
            return value;
        }

        // 本地缓存未命中，从Redis获取
        value = redisCache.get(key, type);
        if (value != null) {
            log.debug("从redis缓存获取值成功：{}", key);

            // 获取Redis中该键的剩余过期时间
            long remainingTtl = redissonClient.getBucket(key).remainTimeToLive();

            // 设置本地缓存的过期时间
            long localExpireSeconds = 0;
            if (remainingTtl > 0) {
                // remainingTtl是毫秒，转换为秒
                localExpireSeconds = remainingTtl / 1000;
            } else if (remainingTtl == -1) {
                // redis 中永不过期，将本地缓存设置为10分钟
                localExpireSeconds = 600;
            }

            if (localExpireSeconds > 0) {
                log.debug("设置本地缓存过期时间 {} : {}", key, localExpireSeconds);
                localCache.set(key, value, localExpireSeconds);
            }
        }

        return value;
    }

    /**
     * 设置缓存
     * <p>
     * 写入策略：同时写入本地缓存和Redis缓存
     *
     * @param key           缓存键
     * @param value         缓存值
     * @param expireSeconds 过期时间（秒）
     * @param <T>           值类型
     */
    @Override
    public <T> void set(String key, T value, long expireSeconds) {
        if (Str.isEmpty(key) || value == null) {
            return;
        }

        // 同时写入本地和Redis缓存
        localCache.set(key, value, expireSeconds);
        redisCache.set(key, value, expireSeconds);
    }

    /**
     * 批量设置缓存
     *
     * @param kvMap         键值对映射
     * @param expireSeconds 统一的过期时间（秒）
     * @param <T>           值类型
     */
    @Override
    public <T> void setBatch(Map<String, T> kvMap, long expireSeconds) {
        if (kvMap == null || kvMap.isEmpty()) {
            return;
        }

        // 批量写入Redis
        localCache.setBatch(kvMap, expireSeconds);
        redisCache.setBatch(kvMap, expireSeconds);
    }

    /**
     * 删除指定键的缓存
     * <p>
     * 删除策略：同时删除本地缓存和Redis缓存，并发送失效通知
     *
     * @param key 缓存键
     */
    @Override
    public void delete(String key) {
        if (Str.isEmpty(key)) {
            return;
        }

        // 删除本地缓存
        localCache.delete(key);
        // 删除Redis缓存
        redisCache.delete(key);

        // 发送缓存失效通知
        publishInvalidationMessage(key);
    }

    /**
     * 批量删除缓存
     *
     * @param keys 缓存键的集合
     */
    @Override
    public void deleteBatch(Iterable<String> keys) {
        if (keys == null) {
            return;
        }

        // 批量删除本地缓存
        localCache.deleteBatch(keys);
        // 批量删除Redis缓存
        redisCache.deleteBatch(keys);

        // 发送批量失效通知
        keys.forEach(this::publishInvalidationMessage);
    }

    /**
     * 按前缀删除缓存
     *
     * @param prefix 键前缀
     */
    @Override
    public void deletePattern(String prefix) {
        if (Str.isEmpty(prefix)) {
            return;
        }

        // 删除本地缓存
        localCache.deletePattern(prefix);
        // 删除Redis缓存
        redisCache.deletePattern(prefix);

        // 发送模式失效通知
        publishInvalidationMessage("pattern:" + prefix);
    }

    /**
     * 判断缓存是否存在
     * <p>
     * 先检查本地缓存，再检查Redis缓存
     *
     * @param key 缓存键
     * @return true表示存在，false表示不存在
     */
    @Override
    public boolean exists(String key) {
        if (Str.isEmpty(key)) {
            return false;
        }

        // 先检查本地缓存
        if (localCache.exists(key)) {
            return true;
        }

        // 再检查Redis缓存
        return redisCache.exists(key);
    }

    /**
     * 清空所有缓存
     */
    @Override
    public void clear() {
        localCache.clear();
        redisCache.clear();

        // 发送全局清空通知
        publishInvalidationMessage("clear:all");
    }

    /**
     * 注册缓存失效监听器
     * <p>
     * 监听其他节点发送的缓存失效消息，保持本地缓存一致性
     */
    private void registerInvalidationListener() {
        String channelPattern = cacheInvalidateChannel + "*";

        PatternMessageListener<String> listener = (pattern, channel, message) -> handleInvalidationMessage(message);
        int listenerId = redissonClient.getPatternTopic(channelPattern).addListener(String.class, listener);

        listenerRegistry.put(channelPattern, listenerId);
        log.info("注册缓存失效监听器, 频道: {}, 实例ID: {}", channelPattern, instanceId);
    }

    /**
     * 处理缓存失效消息
     *
     * @param message 失效消息
     */
    private void handleInvalidationMessage(String message) {
        if (Str.isEmpty(message)) {
            return;
        }

        try {
            // 解析消息格式：instanceId:operation:key
            String[] parts = message.split(":", 3);
            if (parts.length < 3) {
                return;
            }

            String sourceInstanceId = parts[0];
            String operation = parts[1];
            String key = parts[2];

            // 忽略自己发送的消息
            if (instanceId.equals(sourceInstanceId)) {
                return;
            }

            // 根据操作类型处理本地缓存
            switch (operation) {
                case "delete":
                    localCache.delete(key);
                    log.debug("收到缓存失效消息，删除本地缓存, key: {}", key);
                    break;
                case "pattern":
                    localCache.deletePattern(key);
                    log.debug("收到缓存失效消息，按前缀删除本地缓存, prefix: {}", key);
                    break;
                case "clear":
                    localCache.clear();
                    log.debug("收到缓存失效消息，清空本地缓存");
                    break;
                default:
                    log.warn("未知的缓存失效操作: {}", operation);
            }
        } catch (Exception e) {
            log.error("处理缓存失效消息失败: {}", message, e);
        }
    }

    /**
     * 发布缓存失效消息
     *
     * @param key 缓存键或操作标识
     */
    private void publishInvalidationMessage(String key) {
        try {
            String operation;
            String actualKey;

            if (key.startsWith("pattern:")) {
                operation = "pattern";
                actualKey = key.substring(8);
            } else if (key.equals("clear:all")) {
                operation = "clear";
                actualKey = "all";
            } else {
                operation = "delete";
                actualKey = key;
            }

            String message = instanceId + ":" + operation + ":" + actualKey;
            String channel = cacheInvalidateChannel + operation;

            redissonClient.getTopic(channel).publish(message);

            log.debug("发送缓存失效消息, channel: {}, message: {}", channel, message);
        } catch (Exception e) {
            log.error("发送缓存失效消息失败, key: {}", key, e);
        }
    }

    /**
     * 获取本地缓存统计信息
     *
     * @return 本地缓存大小
     */
    public long getLocalCacheSize() {
        return localCache.size();
    }

    /**
     * 销毁资源
     * <p>
     * 取消注册监听器，清理资源
     */
    @PreDestroy
    public void destroy() {
        for (Map.Entry<String, Integer> entry : listenerRegistry.entrySet()) {
            try {
                redissonClient.getPatternTopic(entry.getKey()).removeListener(entry.getValue());
            } catch (Exception e) {
                log.warn("取消注册监听器失败: {}", entry.getKey(), e);
            }
        }
        listenerRegistry.clear();

        log.info("二级缓存实例已销毁, instanceId: {}", instanceId);
    }

}
